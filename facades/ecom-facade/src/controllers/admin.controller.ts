import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Admin, AdminWithRelations} from '../models';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {AdminProxyType} from '../datasources/configs/admin-proxy.config';
import {AdminService} from '../services';
import {service, inject} from '@loopback/core';
import {RestBindings, Request} from '@loopback/rest';

const basePath = '/admins';

export class AdminController {
  constructor(
    @restService(Admin)
    private readonly adminProxy: AdminProxyType,
    @service(AdminService)
    private readonly adminService: AdminService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSubAdmin]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Admin model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Admin)}},
  })
  async create(
    @requestBody.file()
    _request: Request,
  ): Promise<Admin> {
    return this.adminService.createAdmin();
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSubAdmin]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Admin model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Admin) where?: Where<Admin>): Promise<Count> {
    return this.adminProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSubAdmin]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Admin model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Admin),
        },
      },
    },
  })
  async find(
    @param.filter(Admin) filter?: Filter<Admin>,
  ): Promise<AdminWithRelations[]> {
    return this.adminService.getAdminsWithPresignedUrls(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSubAdmin]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Admin model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Admin),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Admin, {exclude: 'where'})
    filter?: FilterExcludingWhere<Admin>,
  ): Promise<AdminWithRelations> {
    return this.adminService.getAdminWithPresignedUrl(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSubAdmin]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody.file()
    _request: Request,
  ): Promise<void> {
    return this.adminService.updateAdmin(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSubAdmin]})
  @patch(`${basePath}/{id}/status`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin status update success',
  })
  async updateAdminStatus(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['status'],
            properties: {
              status: {
                type: 'string',
                enum: ['PENDING', 'APPROVED', 'REJECTED', 'INACTIVE'],
              },
            },
          },
        },
      },
    })
    statusUpdate: {status: string},
  ): Promise<void> {
    await this.adminProxy.updateAdminStatus(
      id,
      statusUpdate,
      this.request.headers.authorization!,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSubAdmin]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.adminProxy.deleteById(id);
  }
}
