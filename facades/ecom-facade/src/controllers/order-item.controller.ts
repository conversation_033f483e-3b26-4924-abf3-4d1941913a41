import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
} from '@loopback/rest';
import {OrderLineItem} from '../models';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {OrderItemStatus, PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {OrderItemService} from '../services';
import {service} from '@loopback/core';
const basePath = '/order-line-items';
export class OrderItemController {
  constructor(
    @restService(OrderLineItem)
    private readonly orderItemProxy: ModifiedRestService<OrderLineItem>,
    @service(OrderItemService)
    private readonly orderService: OrderItemService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'OrderLineItem model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(OrderLineItem)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OrderLineItem, {
            title: 'NewOrderLineItem',
            exclude: ['id'],
          }),
        },
      },
    })
    orderLineItem: Omit<OrderLineItem, 'id'>,
  ): Promise<OrderLineItem> {
    return this.orderItemProxy.create(orderLineItem);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'OrderLineItem model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.header.string('x-origin') xOrigin: string,
    @param.where(OrderLineItem) where?: Where<OrderLineItem>,
  ): Promise<Count> {
    where = await this.orderService.applyOrderItemScopeWhere(where, xOrigin);
    return this.orderItemProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of OrderLineItem model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(OrderLineItem, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.header.string('x-origin') xOrigin: string,
    @param.filter(OrderLineItem) filter?: Filter<OrderLineItem>,
  ): Promise<OrderLineItem[]> {
    filter = await this.orderService.applyOrderItemScopeFilter(filter, xOrigin);
    const orderLineItems = await this.orderItemProxy.find(filter);
    return this.orderService.getOrderItemsWithPreviewUrl(orderLineItems);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewOrder]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'OrderLineItem model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(OrderLineItem, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(OrderLineItem, {exclude: 'where'})
    filter?: FilterExcludingWhere<OrderLineItem>,
  ): Promise<OrderLineItem> {
    const orderItem = await this.orderItemProxy.findById(id, filter);
    return this.orderService.getOrderItemWithPreviewUrl(orderItem);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateOrder]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'OrderLineItem PATCH success',
      },
    },
  })
  async updateById(
    @param.header.string('x-origin') xOrigin: string,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OrderLineItem, {partial: true}),
        },
      },
    })
    orderLineItem: OrderLineItem,
    @param.header.string('Authorization') token?: string,
  ): Promise<void> {
    if (xOrigin === 'ecomdukes-admin') {
      if (orderLineItem.status === OrderItemStatus.Cancelled) {
        orderLineItem = {
          ...orderLineItem,
          cancelledByAdmin: true,
        } as OrderLineItem;
      }
    }
    await this.orderItemProxy.updateById(id, orderLineItem);
    const updatedItem = await this.orderItemProxy.findById(id);
    if (updatedItem.orderId) {
      await this.orderService.createPromoUsageIfApplicable(
        updatedItem.orderId,
        token,
      );
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteOrder]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'OrderLineItem DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.orderItemProxy.deleteById(id);
  }

  // @authenticate(STRATEGY.BEARER)
  // @authorize({
  //   permissions: [PermissionKeys.UpdateOrder],
  // })
  // @post(`${basePath}/{id}/cancel-order`, {
  //   responses: {
  //     [STATUS_CODE.NO_CONTENT]: {
  //       description: 'Cancel an order line item',
  //     },
  //   },
  // })
  // async cancelOrderItem(
  //   @param.path.string('id') id: string,
  //   @param.header.string('x-origin') xOrigin: string,
  //   @requestBody({
  //     content: {
  //       [CONTENT_TYPE.JSON]: {
  //         schema: {
  //           type: 'object',
  //           properties: {
  //             reason: {type: 'string'},
  //           },
  //           required: [],
  //         },
  //       },
  //     },
  //   })
  //   body: {reason?: string},
  // ): Promise<void> {
  //   await this.orderService.cancelOrderItem(id, xOrigin);
  // }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [PermissionKeys.UpdateOrder],
  })
  @patch('order-line-items/{orderItemId}/status/{newStatus}', {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cancel an order line item',
      },
    },
  })
  // controller
  async updateOrderItemStatus(
    @param.path.string('orderItemId') orderItemId: string,
    @param.path.string('newStatus') newStatus: OrderItemStatus,
  ): Promise<void> {
    await this.orderService.updateOrderItemStatus(orderItemId, newStatus);
  }
}
