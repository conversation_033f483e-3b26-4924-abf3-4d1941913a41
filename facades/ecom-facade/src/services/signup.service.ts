// // src/services/signup.service.ts

import {
  injectable,
  BindingScope,
  service,
  inject,
  Getter,
} from '@loopback/core';
import {DukeCoinService} from './duke-coin.service';
import {ReferralProgram} from '../models/ecom-service/referral-program.model';
import {Referral} from '../models/ecom-service/referral.model';
import {
  IAuthUserWithPermissions,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {DukeCoin} from '../models/ecom-service/duke-coin.model';
import {ReferralStatus, TransactionType, UserOrigin} from '@local/core';
import {
  AuthCodeBindings,
  AuthTokenRequest,
  AuthUser,
  CodeWriterFn,
  LocalUserProfileDto,
  LoginRequest,
} from '@sourceloop/authentication-service';
import jwt from 'jsonwebtoken';
import {AuthProxyType} from '../datasources/configs/auth-proxy.config';
import {systemUser} from '../constants';
import {AuthenticationBindings} from 'loopback4-authentication';
import {Groups, IAuthUserWithTenant, Topic, UserFcm} from '../models';
import {HttpErrors} from '@loopback/rest';
import {AnyObject} from '@loopback/repository';
import {ListProxyType} from '../datasources/configs/list-proxy.config';
import {List} from '../models/notification/list.model';
import {GroupProxyType} from '../datasources/configs';
import {PermissionKey} from '../enums';
import {TopicProxyType} from '../datasources/configs/topic-proxy.config';

@injectable({scope: BindingScope.TRANSIENT})
export class SignupService {
  constructor(
    @restService(ReferralProgram)
    public referralProgramProxy: ModifiedRestService<ReferralProgram>,
    @restService(Referral)
    public referralProxy: ModifiedRestService<Referral>,
    @service(DukeCoinService)
    private readonly dukeCoinService: DukeCoinService,
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,

    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(List)
    private readonly listService: ListProxyType,
    @restService(Groups)
    private readonly groupService: GroupProxyType,
    @restService(UserFcm)
    private readonly userFcmProxy: ModifiedRestService<UserFcm>,
    @restService(Topic)
    private readonly topicService: TopicProxyType,
  ) {}

  async signupWithReferralAndToken(
    req: AnyObject,
    xOrigin: string,
    token: string,
  ) {
    const codePayload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKey.CreateDukeCoin,
        PermissionKey.ViewDukeCoin,
        PermissionKey.UpdateDukeCoin,
        PermissionKey.UpdateReferral,
        PermissionKey.UpdateReferralProgram,
        PermissionKey.UpdateDukeCoin,
        PermissionKey.CreateReferralProgram,
        PermissionKey.ViewReferralProgram,
        PermissionKey.ViewReferral,
        PermissionKey.CreateReferral,
        PermissionKey.CreateUser,
        PermissionKey.Onboard,
        PermissionKey.ViewUser,
        PermissionKey.ViewConfiguration,
      ],
    };
    const {referralCode, subscribeToNewsletter, fcmToken, ...signupRequest} =
      req;
    const signupResponse = await this.authProvider.signupWithToken(
      signupRequest as LocalUserProfileDto,
      token,
      xOrigin,
    );

    const password = signupResponse.user.password;
    const email = signupResponse.user.email;
    const clientId = signupResponse.user.clientId;
    const clientSecret = signupResponse.user.clientSecret;

    const loginRequest = new LoginRequest({
      username: email,
      password,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      client_id: clientId,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      client_secret: clientSecret,
    });

    const codeToken = await this.authProvider.login(loginRequest);
    const authTokenRequest = new AuthTokenRequest({
      code: codeToken.code,
      clientId: clientId,
    });

    const userToken = await this.authProvider.getToken(authTokenRequest);

    const referralToken = await this.codeWriter(
      jwt.sign(codePayload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
    const decodedToken = jwt.decode(userToken.accessToken) as AnyObject;

    if (referralCode) {
      const [referral] = await this.referralProxy.find(
        {
          where: {referralCode: referralCode},
        },
        `Bearer ${referralToken}`,
      );

      if (referral && referral.status === ReferralStatus.OPEN) {
        const [referralProgram] = await this.referralProgramProxy.find(
          {
            where: {type: referral.type},
          },
          `Bearer ${referralToken}`,
        );

        if (referralProgram) {
          const userTenantId =
            decodedToken?.userTenantId ||
            decodedToken?.tenantId ||
            decodedToken?.tid;

          const refereeCoin: Partial<DukeCoin> = {
            coins: Number(referralProgram.refereeCoins),
            coinsChanged: Number(referralProgram.refereeCoins),
            referralId: referral.id,
            transactionType: TransactionType.Earn,
            description: 'Referral bonus for signup (referee)',
            userTenantId: userTenantId,
          };

          await this.dukeCoinService.createDukeCoin(
            refereeCoin,
            `Bearer ${referralToken}`,
          );

          const referrerCoin: Partial<DukeCoin> = {
            coins: Number(referralProgram.referrerCoins),
            coinsChanged: Number(referralProgram.referrerCoins),
            referralId: referral.id,
            transactionType: TransactionType.Earn,
            description: 'Referral bonus for referrer',
            userTenantId: referral.createdBy,
          };
          await this.dukeCoinService.createDukeCoin(
            referrerCoin,
            `Bearer ${referralToken}`,
          );

          const referralRecord = await this.referralProxy.findById(
            referral.id as string,
            undefined,
            `Bearer ${referralToken}`,
          );

          if (!referralRecord.referredId) {
            await this.referralProxy.updateById(
              referral.id as string,
              {referredId: userTenantId, status: ReferralStatus.USED},
              `Bearer ${referralToken}`,
            );
          }
        }
      } else {
        throw new HttpErrors.BadRequest(
          `Invalid or already used referral code: ${referralCode}`,
        );
      }
    }

    // Run background processes without blocking signup response
    this.processPostSignupTasks(
      decodedToken,
      signupRequest,
      xOrigin,
      fcmToken,
      subscribeToNewsletter,
    ).catch(error => {
      console.error('Failed to process post-signup tasks:', error);
      // Don't throw error to avoid breaking the signup process
    });

    return signupResponse;
  }

  private async processPostSignupTasks(
    decodedToken: AnyObject,
    signupRequest: AnyObject,
    xOrigin: string,
    fcmToken?: string,
    subscribeToNewsletter?: boolean,
  ): Promise<void> {
    try {
      const notificationUserPayload = {
        ...decodedToken,
        permissions: [
          ...decodedToken.permissions,
          PermissionKey.ViewCampaign,
          PermissionKey.CreateCampaign,
        ],
      };
      const notificationToken = await this.codeWriter(
        jwt.sign(notificationUserPayload, process.env.JWT_SECRET as string, {
          audience: '',
          algorithm: 'HS256',
        }),
      );

      // Process user origin-specific subscription
      if (xOrigin === UserOrigin.Seller || xOrigin === UserOrigin.Customer) {
        await this.processUserOriginSubscription(
          xOrigin,
          signupRequest,
          notificationToken,
        );
      }

      // Process FCM token
      if (fcmToken) {
        await this.processFcmToken(fcmToken, decodedToken, notificationToken);
      }

      // Process newsletter subscription
      if (subscribeToNewsletter) {
        await this.processNewsletterSubscription(
          signupRequest,
          fcmToken,
          notificationToken,
        );
      }
    } catch (error) {
      console.error('Error in post-signup tasks:', error);
      // Don't throw error to avoid breaking the signup process
    }
  }

  private async processUserOriginSubscription(
    xOrigin: string,
    signupRequest: AnyObject,
    notificationToken: string,
  ): Promise<void> {
    try {
      const topic = xOrigin === UserOrigin.Seller ? 'seller' : 'customer';
      const filter = {where: {groupId: topic}};
      const groups = await this.groupService.findGroups(
        `Bearer ${notificationToken}`,
        filter,
      );

      const group = groups[0];

      if (group) {
        await this.listService.subscribeContactToList(
          group.listKey,
          {
            firstName: signupRequest.firstName,
            lastName: signupRequest.lastName,
            email: signupRequest.email,
          },
          group.topicId,
          `Bearer ${notificationToken}`,
        );
        console.log(`Successfully subscribed ${xOrigin} to ${topic} group`);
      }
    } catch (error) {
      console.error(`Failed to process ${xOrigin} subscription:`, error);
    }
  }

  private async processFcmToken(
    fcmToken: string,
    decodedToken: AnyObject,
    notificationToken: string,
  ): Promise<void> {
    try {
      await this.userFcmProxy.create(
        {
          fcmToken: fcmToken,
          deviceId: 'web',
          userTenantId: decodedToken.userTenantId,
        },
        `Bearer ${notificationToken}`,
      );
      console.log('Successfully created FCM token record');
    } catch (error) {
      console.error('Failed to process FCM token:', error);
    }
  }

  private async processNewsletterSubscription(
    signupRequest: AnyObject,
    fcmToken: string | undefined,
    notificationToken: string,
  ): Promise<void> {
    try {
      const allGroups = await this.groupService.findGroups(
        `Bearer ${notificationToken}`,
        {},
      );

      await Promise.all(
        allGroups.map(async group => {
          try {
            // Subscribe to email list
            await this.listService.subscribeContactToList(
              group.listKey,
              {
                firstName: signupRequest.firstName,
                lastName: signupRequest.lastName,
                email: signupRequest.email,
              },
              group.topicId,
              `Bearer ${notificationToken}`,
            );

            // Subscribe to FCM topic if fcmToken is provided
            if (fcmToken) {
              try {
                await this.topicService.subscribeToFcmTopic(
                  group.name,
                  fcmToken,
                  `Bearer ${notificationToken}`,
                );
              } catch (error) {
                console.error(
                  `Failed to subscribe to FCM topic ${group.groupId}:`,
                  error,
                );
              }
            }
          } catch (error) {
            console.error(`Failed to subscribe to group ${group.name}:`, error);
          }
        }),
      );
      console.log('Successfully processed newsletter subscription');
    } catch (error) {
      console.error('Failed to process newsletter subscription:', error);
    }
  }
}
