import {injectable, BindingScope, service, inject} from '@loopback/core';
import {FileUploadService} from './file-upload.service';
import {S3HelperService} from './s3-helper.service';
import {RestBindings, Response, Request, HttpErrors} from '@loopback/rest';
import {restService} from '@sourceloop/core';
import {Admin, AdminDto, AdminWithRelations} from '../models';
import {AdminProxyType} from '../datasources/configs/admin-proxy.config';
import {multerMiddleware} from '../middlewares';
import {ALLOWED_STORE_FILE_EXTENTIONS} from '@local/core';
import {UPLOAD_FILE_SIZE} from '../constants';

@injectable({scope: BindingScope.TRANSIENT})
export class AdminService {
  private token: string;
  constructor(
    @service(FileUploadService)
    private readonly fileUploadService: FileUploadService,
    @service(S3HelperService)
    private readonly s3HelperService: S3HelperService,
    @inject(RestBindings.Http.RESPONSE)
    private readonly response: Response,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(Admin)
    private readonly adminProxy: AdminProxyType,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async parseProfileDto(request: Request) {
    await new Promise<void>((resolve, reject) => {
      multerMiddleware.any()(request, this.response, (err: unknown) => {
        if (err) {
          reject(new HttpErrors.UnprocessableEntity('File upload error'));
        } else {
          resolve();
        }
      });
    });
  }

  async createAdmin(): Promise<Admin> {
    await this.parseProfileDto(this.request);
    const admin: AdminDto = this.request.body;
    admin.username = admin.email ?? '';
    if (admin.dob) {
      admin.dob = new Date(admin.dob);
    }
    if (typeof admin.permissions === 'string') {
      admin.permissions = JSON.parse(admin.permissions);
    }
    const files = this.request.files as Express.Multer.File[];
    const profileFile = files?.find(
      (file: Express.Multer.File) => file.fieldname === 'photoUrl',
    );
    if (profileFile) {
      this.fileUploadService.validateExtension(
        ALLOWED_STORE_FILE_EXTENTIONS,
        profileFile,
      );
      this.fileUploadService.validateSize(UPLOAD_FILE_SIZE, profileFile);
      admin.photoUrl = await this.s3HelperService.uploadFileToS3(
        profileFile,
        process.env.AWS_S3_BUCKET!,
      );
    }
    return this.adminProxy.createAdmin(admin, this.token);
  }

  async updateAdmin(id: string): Promise<void> {
    await this.parseProfileDto(this.request);
    const admin: AdminDto = this.request.body;
    admin.username = admin.email ?? '';
    if (admin.dob) {
      admin.dob = new Date(admin.dob);
    }
    if (typeof admin.permissions === 'string') {
      admin.permissions = JSON.parse(admin.permissions);
    }
    const files = this.request.files as Express.Multer.File[];
    const profileFile = files?.find(
      (file: Express.Multer.File) => file.fieldname === 'photoUrl',
    );

    if (profileFile) {
      this.fileUploadService.validateExtension(
        ALLOWED_STORE_FILE_EXTENTIONS,
        profileFile,
      );
      this.fileUploadService.validateSize(UPLOAD_FILE_SIZE, profileFile);
      admin.photoUrl = await this.s3HelperService.uploadFileToS3(
        profileFile,
        process.env.AWS_S3_BUCKET!,
      );
    }

    await this.adminProxy.updateAdminById(id, admin, this.token);
  }

  async getAdminWithPresignedUrl(
    id: string,
    filter?: any,
  ): Promise<AdminWithRelations> {
    const admin = (await this.adminProxy.findById(
      id,
      filter,
    )) as AdminWithRelations;

    let preSignedUrl: string | null = null;
    const bucketName = process.env.AWS_S3_BUCKET!;
    if (admin.userTenant?.user?.photoUrl) {
      preSignedUrl = `${process.env.CDN_ORIGIN}/${admin.userTenant.user.photoUrl}`;
    }

    return {
      ...admin,
      preSignedPhotoUrl: preSignedUrl,
    } as AdminWithRelations;
  }

  async getAdminsWithPresignedUrls(
    filter?: any,
  ): Promise<AdminWithRelations[]> {
    const admins = (await this.adminProxy.find(filter)) as AdminWithRelations[];
    const cdnOrigin = process.env.CDN_ORIGIN!;

    return Promise.all(
      admins.map(async admin => {
        const preSignedPhotoUrl = admin.userTenant?.user?.photoUrl
          ? `${cdnOrigin}/${admin.userTenant.user.photoUrl}`
          : null;
        return {
          ...admin,
          preSignedPhotoUrl,
        } as AdminWithRelations;
      }),
    );
  }
}
