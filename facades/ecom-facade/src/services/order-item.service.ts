import {injectable, BindingScope, inject, Getter} from '@loopback/core';
import {
  IAuthUserWithTenant,
  Order,
  OrderLineItem,
  OrderLineItemWithRelations,
  PromoUsage,
  Seller,
  ShippingMethod,
} from '../models';
import {Filter, Where} from '@loopback/repository';
import {AuthenticationBindings} from 'loopback4-authentication';
import {OrderItemStatus, SellerStatus, ShippingMethodType} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {OrderProxyType, SellerProxyType} from '../datasources/configs';
import {PromoUsageProxyType} from '../datasources/configs/promo-usage-proxy.config';
import {NotificationHelperService} from './notification-helper.service';

const allowedOrderItemTransitions: Record<OrderItemStatus, OrderItemStatus[]> =
  {
    [OrderItemStatus.New]: [OrderItemStatus.Pending],
    [OrderItemStatus.Pending]: [
      OrderItemStatus.Accepted,
      OrderItemStatus.Rejected,
      OrderItemStatus.Cancelled,
    ],
    [OrderItemStatus.Paid]: [OrderItemStatus.Accepted],
    [OrderItemStatus.Accepted]: [
      OrderItemStatus.Processing,
      OrderItemStatus.ReadyToDispatch,
    ],
    [OrderItemStatus.Processing]: [OrderItemStatus.Dispatched],
    [OrderItemStatus.Dispatched]: [OrderItemStatus.Delivered],
    [OrderItemStatus.Delivered]: [OrderItemStatus.ReturnRefund],
    [OrderItemStatus.ReturnRefund]: [OrderItemStatus.RefundCompleted],
    [OrderItemStatus.RefundCompleted]: [],
    [OrderItemStatus.Rejected]: [],
    [OrderItemStatus.Cancelled]: [],
    [OrderItemStatus.ReadyToDispatch]: [OrderItemStatus.Dispatched],
  };

@injectable({scope: BindingScope.TRANSIENT})
export class OrderItemService {
  constructor(
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Seller)
    private readonly sellerProxy: SellerProxyType,
    @restService(Order)
    private orderProxy: OrderProxyType,
    @restService(PromoUsage)
    private promoUsageProxy: PromoUsageProxyType,
    @restService(OrderLineItem)
    private orderItemProxy: ModifiedRestService<OrderLineItem>,
    @inject('services.NotificationHelperService')
    private readonly notificationHelperService: NotificationHelperService,
    @restService(ShippingMethod)
    private readonly shippingProxyService: ModifiedRestService<ShippingMethod>,
  ) {}

  async getOrderItemsWithPreviewUrl(
    orderItems: OrderLineItemWithRelations[],
  ): Promise<OrderLineItemWithRelations[]> {
    if (orderItems.length) {
      for (const item of orderItems) {
        if (item.productVariant) {
          const asset = item.productVariant?.featuredAsset;
          if (asset?.preview && process.env.CDN_ORIGIN) {
            asset.previewUrl = `${process.env.CDN_ORIGIN}/${asset.preview}`;
          }
        }
      }
    }
    return orderItems;
  }

  async getOrderItemWithPreviewUrl(
    orderItem: OrderLineItemWithRelations,
  ): Promise<OrderLineItemWithRelations> {
    if (orderItem) {
      if (orderItem.productVariant) {
        const asset = orderItem.productVariant?.featuredAsset;
        if (asset?.preview && process.env.CDN_ORIGIN) {
          asset.previewUrl = `${process.env.CDN_ORIGIN}/${asset.preview}`;
        }
      }
    }
    return orderItem;
  }

  async applyOrderItemScopeFilter(
    baseFilter: Filter<OrderLineItem> = {},
    origin: string,
  ): Promise<Filter<OrderLineItem>> {
    const where = baseFilter.where ?? {};
    const currentUser = await this.getCurrentUser();
    if (origin === 'ecomdukes-seller') {
      const seller = await this.getSeller();
      Object.assign(where, {sellerId: seller.id});
    } else if (origin === 'ecomdukes-customer') {
      Object.assign(where, {createdBy: currentUser.userTenantId});
    }

    return {
      ...baseFilter,
      where,
    };
  }

  async applyOrderItemScopeWhere(
    baseWhere: Where<OrderLineItem> = {},
    origin: string,
  ): Promise<Where<OrderLineItem>> {
    const where = {...baseWhere}; // Create a copy to avoid mutating the input
    const currentUser = await this.getCurrentUser();

    if (origin === 'ecomdukes-seller') {
      const seller = await this.getSeller();
      Object.assign(where, {sellerId: seller.id});
    } else if (origin === 'ecomdukes-customer') {
      Object.assign(where, {createdBy: currentUser.userTenantId});
    }

    return where;
  }

  private async getSeller(): Promise<Seller> {
    const user = await this.getCurrentUser();
    const sellers = await this.sellerProxy.find({
      where: {
        userTenantId: user.userTenantId ?? '',
        status: SellerStatus.APPROVED,
      },
      limit: 1,
    });

    if (!sellers?.length) {
      throw new HttpErrors.BadRequest(
        `Your account is either suspended or cannot be found in our system. Please contact support.`,
      );
    }
    return sellers[0];
  }

  async createPromoUsageIfApplicable(
    orderId: string,
    token?: string,
  ): Promise<void> {
    const user = await this.getCurrentUser();
    const sellers = await this.sellerProxy.find({
      where: {
        userTenantId: user.userTenantId ?? '',
      },
      limit: 1,
    });

    if (!sellers.length || sellers[0].status !== SellerStatus.APPROVED) {
      return;
    }

    const order = await this.orderProxy.findById(orderId);
    if (!order) {
      throw new HttpErrors.NotFound('Order not found');
    }

    if (!order.promoCodeId || !order.customerId) {
      return;
    }

    const usage = new PromoUsage({
      promoCodeId: order.promoCodeId,
      customerId: order.customerId,
      orderId: order.id,
    });

    await this.promoUsageProxy.createPromoUsage(usage, token);
  }

  // async cancelOrderItem(id: string, xOrigin: string): Promise<void> {
  //   const existing = (await this.orderItemProxy.findById(id, {
  //     include: [
  //       {
  //         relation: 'seller',
  //         scope: {
  //           include: [
  //             {
  //               relation: 'userTenant',
  //               scope: {
  //                 include: [
  //                   {
  //                     relation: 'user',
  //                     scope: {
  //                       fields: {
  //                         firstName: true,
  //                         lastName: true,
  //                         email: true,
  //                         photoUrl: true,
  //                       },
  //                     },
  //                   },
  //                 ],
  //               },
  //             },
  //           ],
  //         },
  //       },
  //     ],
  //   })) as OrderLineItemWithRelations;
  //   if (!existing) {
  //     throw new HttpErrors.NotFound('Order item not found');
  //   }

  //   if (existing.status === OrderItemStatus.Processing) {
  //     throw new HttpErrors.BadRequest(
  //       'Cancellation not allowed. The product is currently in processing state.',
  //     );
  //   }

  //   const updatePayload: Partial<OrderLineItem> = {
  //     status: OrderItemStatus.Cancelled,
  //   };

  //   if (xOrigin === 'ecomdukes-admin') {
  //     updatePayload.cancelledByAdmin = true;
  //   }

  //   await this.orderItemProxy.updateById(id, updatePayload);
  //   //email send to seller

  //   const sellerEmail = existing.seller?.userTenant?.user?.email;
  //   const sellerName =
  //     (existing.seller?.userTenant?.user?.firstName &&
  //       existing.seller?.userTenant?.user?.lastName &&
  //       `${existing.seller.userTenant.user.firstName} ${existing.seller.userTenant.user.lastName}`) ||
  //     existing.seller?.userTenant?.user?.firstName ||
  //     existing.seller?.userTenant?.user?.email ||
  //     'Seller';

  //   if (sellerEmail) {
  //     await this.notificationHelperService.sendEmail(
  //       'order-cancellation-seller.hbs',
  //       'Order Cancelled Notification',
  //       {
  //         sellerName,
  //         orderId: existing.orderId,
  //         productName: existing.productVariant?.product?.name ?? 'N/A',
  //       },
  //       sellerEmail,
  //       sellerName,
  //     );
  //   }
  // }

  // in OrderItemService

  // async updateOrderItemStatus(
  //   orderItemId: string,
  //   newStatus: OrderItemStatus,
  // ): Promise<void> {
  //   const orderItem = (await this.orderItemProxy.findById(orderItemId, {
  //     include: [
  //       {
  //         relation: 'seller',
  //         scope: {
  //           include: [
  //             {
  //               relation: 'userTenant',
  //               scope: {
  //                 include: [
  //                   {
  //                     relation: 'user',
  //                     scope: {
  //                       fields: {
  //                         firstName: true,
  //                         lastName: true,
  //                         email: true,
  //                         photoUrl: true,
  //                       },
  //                     },
  //                   },
  //                 ],
  //               },
  //             },
  //           ],
  //         },
  //       },
  //       {
  //         relation: 'order',
  //         scope: {
  //           include: [
  //             {
  //               relation: 'user',
  //               scope: {
  //                 fields: {
  //                   firstName: true,
  //                   lastName: true,
  //                   email: true,
  //                 },
  //               },
  //             },
  //           ],
  //         },
  //       },
  //       {
  //         relation: 'productVariant',
  //         scope: {
  //           include: [{relation: 'product'}],
  //         },
  //       },
  //     ],
  //   })) as OrderLineItemWithRelations;

  //   if (!orderItem) {
  //     throw new HttpErrors.NotFound('Order item not found');
  //   }

  //   function isOrderItemStatus(value: string): value is OrderItemStatus {
  //     return Object.values(OrderItemStatus).includes(value as OrderItemStatus);
  //   }
  //   let currentStatus: OrderItemStatus;

  //   if (isOrderItemStatus(orderItem.status)) {
  //     currentStatus = orderItem.status;
  //   } else {
  //     throw new Error('Invalid status');
  //   }

  //   if (currentStatus === newStatus) {
  //     return;
  //   }

  //   await this.validateAndUpdateItemStatus(
  //     currentStatus,
  //     newStatus,
  //     orderItem.productVariantId,
  //     async validatedStatus => {
  //       const payload: Partial<OrderLineItem> = {status: validatedStatus};

  //       if (
  //         validatedStatus === OrderItemStatus.Cancelled &&
  //         orderItem.cancelledByAdmin
  //       ) {
  //         payload.cancelledByAdmin = true;
  //       }

  //       await this.orderItemProxy.updateById(orderItemId, payload);

  //       const productName = orderItem.productVariant?.product?.name ?? 'N/A';
  //       const orderId = orderItem.orderId;

  //       if (validatedStatus === OrderItemStatus.Cancelled) {
  //         // ✅ Send to seller
  //         const sellerUser = orderItem.seller?.userTenant?.user;
  //         const sellerEmail = sellerUser?.email;
  //         const sellerName =
  //           (sellerUser?.firstName &&
  //             sellerUser?.lastName &&
  //             `${sellerUser.firstName} ${sellerUser.lastName}`) ||
  //           sellerUser?.firstName ||
  //           sellerUser?.email ||
  //           'Seller';

  //         if (sellerEmail) {
  //           await this.notificationHelperService.sendEmail(
  //             'order-cancellation-seller.hbs',
  //             'Order Cancelled Notification',
  //             {
  //               sellerName,
  //               orderId,
  //               productName,
  //             },
  //             sellerEmail,
  //             sellerName,
  //           );
  //         }
  //       } else {
  //         // ✅ Send to customer
  //         const customer = orderItem.order?.customer;
  //         const customerEmail = customer?.userTenant?.user?.email;
  //         const customerName =
  //           (customer?.userTenant?.user?.firstName &&
  //             customer?.userTenant?.user?.lastName &&
  //             `${customer.userTenant?.user?.firstName} ${customer.userTenant?.user?.lastName}`) ||
  //           customer?.userTenant?.user?.firstName ||
  //           customer?.userTenant?.user?.email ||
  //           'Customer';

  //         if (customerEmail) {
  //           await this.notificationHelperService.sendEmail(
  //             'order-status-update-customer.hbs',
  //             `Your Order Status Updated to ${validatedStatus}`,
  //             {
  //               customerName,
  //               orderId,
  //               productName,
  //               oldStatus: currentStatus,
  //               newStatus: validatedStatus,
  //             },
  //             customerEmail,
  //             customerName,
  //           );
  //         }
  //       }
  //     },
  //   );
  // }

  async updateOrderItemStatus(
    orderItemId: string,
    newStatus: OrderItemStatus,
  ): Promise<void> {
    const orderItem = (await this.orderItemProxy.findById(orderItemId, {
      include: [
        {
          relation: 'seller',
          scope: {
            include: [
              {
                relation: 'userTenant',
                scope: {
                  include: [
                    {
                      relation: 'user',
                      scope: {
                        fields: {
                          firstName: true,
                          lastName: true,
                          email: true,
                          phone: true, // ✅ ensure phone is fetched
                          photoUrl: true,
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          relation: 'order',
          scope: {
            include: [
              {
                relation: 'user',
                scope: {
                  fields: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phone: true, // ✅ ensure phone is fetched
                  },
                },
              },
            ],
          },
        },
        {
          relation: 'productVariant',
          scope: {
            include: [{relation: 'product'}],
          },
        },
      ],
    })) as OrderLineItemWithRelations;

    if (!orderItem) {
      throw new HttpErrors.NotFound('Order item not found');
    }

    function isOrderItemStatus(value: string): value is OrderItemStatus {
      return Object.values(OrderItemStatus).includes(value as OrderItemStatus);
    }

    let currentStatus: OrderItemStatus;

    if (isOrderItemStatus(orderItem.status)) {
      currentStatus = orderItem.status;
    } else {
      throw new Error('Invalid status');
    }

    if (currentStatus === newStatus) {
      return;
    }

    await this.validateAndUpdateItemStatus(
      currentStatus,
      newStatus,
      orderItem.productVariantId,
      async validatedStatus => {
        const payload: Partial<OrderLineItem> = {status: validatedStatus};

        if (
          validatedStatus === OrderItemStatus.Cancelled &&
          orderItem.cancelledByAdmin
        ) {
          payload.cancelledByAdmin = true;
        }

        await this.orderItemProxy.updateById(orderItemId, payload);

        const productName = orderItem.productVariant?.product?.name ?? 'N/A';
        const orderId = orderItem.orderId;

        const sellerUser = orderItem.seller?.userTenant?.user;
        const customer = orderItem.order?.customer?.userTenant?.user;

        const sellerName =
          (sellerUser?.firstName &&
            sellerUser?.lastName &&
            `${sellerUser.firstName} ${sellerUser.lastName}`) ||
          sellerUser?.firstName ||
          sellerUser?.email ||
          'Seller';
        console.log('🚀 ~ OrderItemService ~ sellerName:', sellerName);

        const customerName =
          (customer?.firstName &&
            customer?.lastName &&
            `${customer.firstName} ${customer.lastName}`) ||
          customer?.firstName ||
          customer?.email ||
          'Customer';
        console.log('🚀 ~ OrderItemService ~ customerName:', customerName);

        const sellerEmail = sellerUser?.email;
        const sellerPhone = sellerUser?.phone;
        const customerEmail = customer?.email;
        const customerPhone = customer?.phone;

        // 🔁 Notification logic based on status
        const sendToCustomer = async () => {
          if (customerEmail) {
            await this.notificationHelperService.sendEmail(
              'order-status-update-customer.hbs',
              `Your Order Status Updated to ${validatedStatus}`,
              {
                customerName,
                orderId,
                productName,
                oldStatus: currentStatus,
                newStatus: validatedStatus,
              },
              customerEmail,
              customerName,
            );
          }

          if (customerPhone) {
            // await this.notificationHelperService.sendSms(
            //   customerPhone,
            //   `Hi ${customerName}, your order for "${productName}" is now ${validatedStatus}.`,
            // );
          }
        };

        const sendToSeller = async () => {
          if (sellerEmail) {
            console.log(
              '🚀 ~ OrderItemService ~ sendToSeller ~ sellerEmail:',
              sellerEmail,
            );

            await this.notificationHelperService.sendEmail(
              'order-status-update-seller.hbs',
              `Order Status Changed to ${validatedStatus}`,
              {
                sellerName,
                orderId,
                productName,
                newStatus: validatedStatus,
              },
              sellerEmail,
              sellerName,
            );
          }
          console.log(
            '🚀 ~ OrderItemService ~ sendToSeller ~ sellerEmail1234:',
            sellerEmail,
          );

          if (sellerPhone) {
            // await this.notificationHelperService.sendSms(
            //   sellerPhone,
            //   `Hi ${sellerName}, the status of order "${productName}" is now ${validatedStatus}.`,
            // );
          }
        };

        switch (validatedStatus) {
          case OrderItemStatus.Pending:
          case OrderItemStatus.Paid:
            await Promise.all([sendToCustomer(), sendToSeller()]);
            break;

          case OrderItemStatus.Accepted:
          case OrderItemStatus.Rejected:
            await sendToCustomer();
            break;

          case OrderItemStatus.Cancelled:
            await sendToSeller();
            break;

          default:
            // Optional: fallback
            break;
        }
      },
    );
  }

  async validateAndUpdateItemStatus(
    currentStatus: OrderItemStatus,
    newStatus: OrderItemStatus,
    productVariantId: string,
    updateFn: (newStatus: OrderItemStatus) => Promise<void>,
  ): Promise<void> {
    const allowed = allowedOrderItemTransitions[currentStatus] || [];

    if (
      currentStatus === OrderItemStatus.Processing &&
      newStatus === OrderItemStatus.Dispatched
    ) {
      const [shippingMethod] = await this.shippingProxyService.find({
        where: {type: ShippingMethodType.SELF_SHIPPING},
      });

      if (
        !shippingMethod ||
        shippingMethod.type !== ShippingMethodType.SELF_SHIPPING
      ) {
        throw new HttpErrors.BadRequest(
          `Order can only be marked as "${OrderItemStatus.Dispatched}" from "${OrderItemStatus.Processing}" if using self-shipping.`,
        );
      }
    }

    if (!allowed.includes(newStatus)) {
      throw new HttpErrors.BadRequest(
        `Invalid status transition from "${currentStatus}" to "${newStatus}".`,
      );
    }

    await updateFn(newStatus);
  }
}
