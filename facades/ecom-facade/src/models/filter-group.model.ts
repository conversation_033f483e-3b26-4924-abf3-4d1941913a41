import {Model, model, property} from '@loopback/repository';
import {FilterValue} from './filter-value.model';

@model()
export class FilterGroup extends Model {
  @property({
    type: 'string',
    required: true,
  })
  label: string;

  @property({
    type: 'string',
    required: false,
    jsonSchema: {
      default: false,
    },
  })
  isFacet?: boolean;

  @property({
    type: 'array',
    itemType: FilterValue,
    required: true,
  })
  values: FilterValue[];

  @property({
    type: 'object',
    required: false,
    jsonSchema: {
      type: 'object',
      properties: {
        min: {type: 'number'},
        max: {type: 'number'},
        type: {type: 'string', enum: ['slider']},
      },
      required: ['min', 'max', 'type'],
    },
  })
  metadata?: {
    min?: number;
    max?: number;
    type?: 'slider';
  };

  constructor(data?: Partial<FilterGroup>) {
    super(data);
  }
}
