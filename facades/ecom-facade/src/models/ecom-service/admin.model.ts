import {
  belongsTo,
  hasMany,
  hasOne,
  model,
  property,
} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  UserLevelPermission,
  UserTenant,
  UserTenantWithRelations,
} from '@sourceloop/authentication-service';

@model()
export class Admin extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'admin_id',
  })
  adminId: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'email_verified',
  })
  emailVerified?: boolean;

  @property({
    type: 'boolean',
    default: false,
    name: 'phone_verified',
  })
  phoneVerified?: boolean;

  @property({
    type: 'string',
  })
  status?: string;

  @belongsTo(
    () => UserTenant,
    {keyTo: 'id'},
    {name: 'user_tenant_id', required: true},
  )
  userTenantId: string;

  @hasMany(() => UserLevelPermission, {keyTo: 'userTenantId'})
  permissions: UserLevelPermission[];

  constructor(data?: Partial<Admin>) {
    super(data);
  }
}

export interface AdminRelations {
  permissions?: UserLevelPermission[];
  userTenant: UserTenantWithRelations;
  preSignedPhotoUrl?: string | null;
}

export type AdminWithRelations = Admin & AdminRelations;
