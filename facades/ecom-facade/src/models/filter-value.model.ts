import {Model, model, property} from '@loopback/repository';

@model()
export class FilterValue extends Model {
  @property({
    type: 'string',
    required: true,
  })
  label: string;

  @property({
    type: 'string',
    required: true,
  })
  value: string;

  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  productVariantIds: string[];

  @property({
    type: 'object',
    required: false,
  })
  metadata?: {
    previewUrl?: string;
    parentId?: string | null;
    position?: number;
  };

  constructor(data?: Partial<FilterValue>) {
    super(data);
  }
}
