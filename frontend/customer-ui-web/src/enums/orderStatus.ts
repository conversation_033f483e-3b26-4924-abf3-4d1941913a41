import {OrderItemStatus} from '@local/core/dist/enums';

export enum OrderStatus {
  Pending = 'pending', // Order is placed but not yet processed
  Paid = 'paid', // Payment has been completed
  PaymentFailed = 'payment_failed', // Payment failed during transaction
  Failed = 'failed', // Generic failure (if needed for any other issue)
  Picked = 'picked', // Items have been picked by logistics
  InTransit = 'in_transit', // Order is in transit with the logistics provider
  Delivered = 'delivered', // Order has been delivered to the customer
  DeliveryFailed = 'delivery_failed', // Delivery failed (e.g., customer not available)
  Cancelled = 'cancelled', // Order was cancelled before shipment
  Returned = 'returned', // Order was returned by the customer
  Refunded = 'refunded', // Order was refunded
  RefundInitiated = 'refund_initiated', // Refund process has been started
  RefundFailed = 'refund_failed', // Refund attempt failed (e.g., gateway error)
}

export const orderStatusOptions: {
  label: string;
  value: OrderItemStatus | '';
}[] = [
  {label: 'All', value: ''},
  {label: 'New', value: OrderItemStatus.New},
  {label: 'Pending', value: OrderItemStatus.Pending},
  {label: 'Accepted', value: OrderItemStatus.Accepted},
  {label: 'Processing', value: OrderItemStatus.Processing},
  {label: 'Dispatched', value: OrderItemStatus.Dispatched},
  {label: 'Rejected', value: OrderItemStatus.Rejected},
  {label: 'Return/Refund', value: OrderItemStatus.ReturnRefund},
  {label: 'Delivered', value: OrderItemStatus.Delivered},
  {label: 'Refund Completed', value: OrderItemStatus.RefundCompleted},
  {label: 'Cancelled', value: OrderItemStatus.Cancelled},
];
