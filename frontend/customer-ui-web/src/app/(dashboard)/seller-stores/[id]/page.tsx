'use client';

import {Container, Box, Typography, Grid} from '@mui/material';
import {useParams, useSearchParams} from 'next/navigation';
import {useGetSellerStoreByIdQuery} from 'redux/app/store/sellerStoreApiSlice';
import {ImageValue} from 'types/seller';
import PinnedProducts from 'views/seller-store/PinnedProducts';
import StoreProfileSection from 'views/seller-store/SellerStore';
import StoreNavTabs from 'views/seller-store/StoreNavTabs';
import StoreReviews from 'views/seller-store/StoreReview';
import {useEffect, useMemo} from 'react';
import {useLazyGetProductVariantsQuery} from 'redux/ecom/ecomApiSlice';
import ProductCard from 'views/products/ProductCard';
import {NoProduct} from 'views/products/NoProduct';
import StickyStoreHeader from 'views/seller-store/StickyStoreHeader';
interface IFilter {
  include?: {
    relation: string;
    scope?: {
      where: {
        sellerId: string;
      };
    };
  }[];
}

export default function SellerStorePage() {
  const params = useParams();
  const productSellerId =
    typeof params.id === 'string' ? params.id : (params.id?.[0] ?? '');
  const searchParams = useSearchParams();
  const isNRLView = searchParams?.get('nrl') === 'true';

  const {data: storeData, isLoading: storeLoading} =
    useGetSellerStoreByIdQuery(productSellerId);

  const [
    triggerGetProductVariants,
    {
      data: productVariants = [],
      isLoading: productVariantsLoading,
      isError: productVariantsError,
    },
  ] = useLazyGetProductVariantsQuery();

  const filter: IFilter = useMemo(() => {
    return {
      include: [
        {
          relation: 'product',
          scope: {
            where: {
              sellerId: productSellerId,
            },
            include: [{relation: 'collection'}],
          },
          required: true,
        },
        {
          relation: 'productVariantPrice',
        },
        {
          relation: 'reviews',
        },
        {
          relation: 'featuredAsset',
        },
      ],
    };
  }, [productSellerId]);

  useEffect(() => {
    if (productSellerId) {
      triggerGetProductVariants(filter);
    }
  }, [productSellerId, triggerGetProductVariants, filter]);

  if (storeLoading) {
    return <Typography>Loading store...</Typography>;
  }

  function getImageUrl(image?: ImageValue): string | undefined {
    if (!image) return undefined;
    return typeof image === 'string' ? image : image.path;
  }

  return (
    <Container maxWidth="xl">
      {/* <Box> */}

      <Box>
        <StoreNavTabs
          storeData={
            storeData
              ? {
                  banner: getImageUrl(storeData.banner) ?? '',
                  logo: getImageUrl(storeData.logo),
                  dp: getImageUrl(storeData.dp),
                  storeName: storeData.storeName,
                  description: storeData.description,
                  allowCategorisation: storeData.allowCategorisation,
                }
              : {
                  banner: '',
                }
          }
          isNRLView={isNRLView}
        />
      </Box>
      <Box
        sx={{
          position: 'sticky',
          top: 120,
          zIndex: 1200,
          backgroundColor: 'white',
          borderBottom: '1px solid #eee',
        }}
      >
        <StickyStoreHeader
          storeData={{
            banner: getImageUrl(storeData?.banner) ?? '',
            logo: getImageUrl(storeData?.logo),
            dp: getImageUrl(storeData?.dp),
            storeName: storeData?.storeName,
            allowCategorisation: storeData?.allowCategorisation,
          }}
          productVariants={productVariants}
        />
      </Box>

      <Box>
        <StoreProfileSection
          allowBulkOrder={storeData?.allowBulkOrder}
          sellerId={productSellerId}
          email={storeData?.email}
          phoneNumber={storeData?.phoneNumber}
        />
      </Box>

      <Box>
        <PinnedProducts />
      </Box>

      <Box mt={3} sx={{backgroundColor: 'white', p: 4}}>
        <Typography variant="h3" ml={4}>
          All Products
        </Typography>
        <Grid
          container
          spacing={1}
          justifyContent="flex-start"
          sx={{paddingX: {xs: 1, sm: 2, md: 3}, paddingY: 2}}
        >
          {!productVariants?.length && <NoProduct />}
          {productVariants?.map(product => (
            <Grid item xs={12} sm={6} md={3} key={product.id}>
              <ProductCard productVariant={product} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box>
        <StoreReviews
          productVariants={(productVariants || []).map(variant => ({
            ...variant,
            reviews: (variant.reviews || []).map(review => ({
              id: review.id,
              rating: review.rating,
              review: review.review,
              reviewAssets: review.reviewAssets || [],
              previewAssets: review.previewAssets || [],
              createdOn: review.createdOn,
            })),
          }))}
          isLoading={productVariantsLoading}
          isError={productVariantsError}
        />
      </Box>
    </Container>
  );
}
