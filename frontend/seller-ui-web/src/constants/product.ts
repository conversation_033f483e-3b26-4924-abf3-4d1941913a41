import { ProductDto } from 'types/product-dto';
import { fieldsExcludeMetaFields } from './shared';

export const initialProductValue: ProductDto = {
  name: '',
  enabled: true,
  assets: [],
  options: [],
  facets: [],
  boxContents: [],
  details: undefined,
  specifications: [],
  returnPolicy: undefined,
  terms: undefined,
  sellerId: '',
  featuredAssetId: '',
  collectionId: '',
  description: '',
  variants: [],
  customizations: [],
  disclaimer: undefined,
  isGiftWrapAvailable: false,
  uniqueness: undefined,
  suitability: undefined,
  personalWork: undefined,
  moreInfo: undefined,
  isGiftWrapCharge: 0
};

const directIncludes = [
  'collection',
  'productSpecifications',
  'productDetail',
  'productMoreInfo',
  'productDisclaimer',
  'productReturnPolicy',
  'productTermsAndCondition',
  'productBoxContents',
  'productUniqueness',
  'productPersonalWork',
  'featuredAsset',
  'productDisclaimer',
  'productSuitability'
].map((relation) => ({
  relation,
  scope: {
    fields: fieldsExcludeMetaFields
  }
}));

export const customIncludes = [
  ...directIncludes,
  {
    relation: 'productAssets',
    scope: {
      fields: fieldsExcludeMetaFields,
      order: ['position ASC'],
      include: [
        {
          relation: 'asset',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ]
    }
  },

  {
    relation: 'productFacetValues',
    scope: {
      include: [
        {
          relation: 'facetValue',
          scope: {
            include: [
              {
                relation: 'facet',
                scope: {
                  fields: fieldsExcludeMetaFields
                }
              }
            ],
            fields: fieldsExcludeMetaFields
          }
        }
      ]
    }
  },
  {
    relation: 'productVariants',
    scope: {
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ],
      fields: fieldsExcludeMetaFields
    }
  },
  {
    relation: 'productCustomizationFields',
    scope: {
      fields: fieldsExcludeMetaFields,
      where: {
        productVariantId: null
      },
      include: [
        {
          relation: 'productCustomizationOptions',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ]
    }
  }
];
