'use client';

import { useState, SyntheticEvent, useEffect } from 'react';

// next
import Link from 'next/link';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Links from '@mui/material/Link';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import FormHelperText from '@mui/material/FormHelperText';

// third-party
import * as Yup from 'yup';
import { Formik } from 'formik';

// project-imports
import IconButton from 'components/@extended/IconButton';
import AnimateButton from 'components/@extended/AnimateButton';

// assets
import { Eye, EyeSlash } from 'iconsax-react';
import { useDispatch } from 'react-redux';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { useExchangeTokenMutation, useLoginMutation } from 'redux/auth/authApiSlice';
import { ILoginForm } from 'redux/auth/types';
import { setCredentials } from 'redux/auth/authSlice';
import LoadingButton from 'components/@extended/LoadingButton';

export default function AuthLogin({ providers, csrfToken }: any) {
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };
  const dispatch = useDispatch();
  const handleError = useApiErrorHandler();

  const handleMouseDownPassword = (event: SyntheticEvent) => {
    event.preventDefault();
  };
  const [loginApi, { isLoading, error, reset: loginReset }] = useLoginMutation();
  const [exchangeTokenApi, { isLoading: tokenLoading, error: tokenError, reset: tokenReset }] = useExchangeTokenMutation();

  const handleLogin = async (values: ILoginForm) => {
    const result = await loginApi(values).unwrap();

    if (result) {
      const token = await exchangeTokenApi(result.code).unwrap();
      dispatch(setCredentials(token));
    }
  };

  useEffect(() => {
    if (error) {
      handleError(error);
      loginReset();
    }
    if (tokenError) {
      handleError(tokenError);
      tokenReset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error, handleError, tokenError]);
  return (
    <>
      <Formik
        initialValues={{
          username: '',
          password: ''
        }}
        validationSchema={Yup.object().shape({
          username: Yup.string().email().required('Email is required'),
          password: Yup.string().max(255).required('Password is required')
        })}
        onSubmit={async (values, { setSubmitting }) => {
          setSubmitting(true);
          await handleLogin(values); // Ensure the login function completes before continuing
          setSubmitting(false);
        }}
      >
        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
          <form noValidate onSubmit={handleSubmit}>
            <input name="csrfToken" type="hidden" defaultValue={csrfToken} />
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="email-login">Email Address</InputLabel>
                  <OutlinedInput
                    id="email-login"
                    type="email"
                    value={values.username}
                    name="username"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder="Enter email address"
                    fullWidth
                    error={Boolean(touched.username && errors.username)}
                  />
                </Stack>
                {touched.username && errors.username && (
                  <FormHelperText error id="standard-weight-helper-text-email-login">
                    {errors.username}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="password-login">Password</InputLabel>
                  <OutlinedInput
                    fullWidth
                    error={Boolean(touched.password && errors.password)}
                    id="-password-login"
                    type={showPassword ? 'text' : 'password'}
                    value={values.password}
                    name="password"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                          color="secondary"
                        >
                          {showPassword ? <Eye /> : <EyeSlash />}
                        </IconButton>
                      </InputAdornment>
                    }
                    placeholder="Enter password"
                  />
                </Stack>
                {touched.password && errors.password && (
                  <FormHelperText error id="standard-weight-helper-text-password-login">
                    {errors.password}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} sx={{ mt: -1 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
                  <Links variant="h6" component={Link} href={'/forgot-password'} color="text.secondary">
                    Forgot Password?
                  </Links>
                </Stack>
              </Grid>
              <Grid item xs={12} alignItems={'center'} display={'flex'} justifyContent={'center'}>
                <AnimateButton>
                  <LoadingButton
                    disableElevation
                    disabled={isSubmitting || isLoading || tokenLoading}
                    fullWidth
                    size="large"
                    type="submit"
                    variant="contained"
                    color="primary"
                    sx={{ width: '200px', height: '40px', fontWeight: 'bold' }}
                    loading={isLoading || tokenLoading}
                  >
                    Login
                  </LoadingButton>
                </AnimateButton>
              </Grid>
            </Grid>
          </form>
        )}
      </Formik>
    </>
  );
}
