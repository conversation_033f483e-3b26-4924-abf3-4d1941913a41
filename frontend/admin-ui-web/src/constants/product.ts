import { ProductDto } from 'types/product-dto';
import { fieldsExcludeMetaFields } from './shared';

export const initialProductValue: ProductDto = {
  name: '',
  enabled: true,
  assets: [],
  options: [],
  facets: [],
  boxContents: [],
  details: undefined,
  specifications: [],
  returnPolicy: undefined,
  terms: undefined,
  sellerId: '',
  featuredAssetId: '',
  collectionId: '',
  description: '',
  variants: [],
  customizations: [],
  disclaimer: undefined,
  isGiftWrapAvailable: false,
  isGiftWrapCharge: null,
  uniqueness: undefined,
  suitability: undefined,
  personalWork: undefined
};

const metaFields = ['deleted', 'deletedOn', 'deletedBy', 'createdBy', 'createdOn', 'modifiedOn', 'modifiedBy'];
const directIncludes = [
  'collection',
  'productVariants',
  'productSpecifications',
  'productDetail',
  'productMoreInfo',
  'productDisclaimer',
  'productReturnPolicy',
  'productTermsAndCondition',
  'productBoxContents',
  'productUniqueness',
  'productPersonalWork',
  'featuredAsset',
  'productDisclaimer',
  'productSuitability'
].map((relation) => ({
  relation,
  scope: {
    fields: metaFields.reduce(
      (acc, field) => {
        acc[field] = false;
        return acc;
      },
      {} as Record<string, boolean>
    )
  }
}));

export const customIncludes = [
  ...directIncludes,
  {
    relation: 'productAssets',
    scope: {
      fields: metaFields.reduce(
        (acc, field) => {
          acc[field] = false;
          return acc;
        },
        {} as Record<string, boolean>
      ),
      order: ['position ASC'],
      include: [
        {
          relation: 'asset',
          scope: {
            fields: metaFields.reduce(
              (acc, field) => {
                acc[field] = false;
                return acc;
              },
              {} as Record<string, boolean>
            )
          }
        }
      ]
    }
  },

  {
    relation: 'productFacetValues',
    scope: {
      include: [
        {
          relation: 'facetValue',
          scope: {
            include: [
              {
                relation: 'facet',
                scope: {
                  fields: metaFields.reduce(
                    (acc, field) => {
                      acc[field] = false;
                      return acc;
                    },
                    {} as Record<string, boolean>
                  )
                }
              }
            ],
            fields: metaFields.reduce(
              (acc, field) => {
                acc[field] = false;
                return acc;
              },
              {} as Record<string, boolean>
            )
          }
        }
      ]
    }
  },
  {
    relation: 'productVariants',
    scope: {
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ],
      fields: fieldsExcludeMetaFields
    }
  },
  {
    relation: 'productCustomizationFields',
    scope: {
      where: {
        productVariantId: null
      },
      fields: fieldsExcludeMetaFields,
      include: [
        {
          relation: 'productCustomizationOptions',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ]
    }
  }
];
