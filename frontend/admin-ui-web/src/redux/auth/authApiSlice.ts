import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../apiSlice';
import { TokenResponse } from 'types/auth';
import { ILoginForm } from './types';
import { User } from 'types/user-profile';

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (body: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {
          ...body,
          client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    exchangeToken: builder.mutation<TokenResponse, string>({
      query: (code) => ({
        url: '/auth/token',
        method: 'POST',
        body: {
          clientId: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          code
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        headers: {
          Authorization: `Bearer ${code}`
        }
      })
    }),
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/logout',
        method: 'POST',
        body: { refreshToken },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getUser: builder.query<User, void>({
      query: () => ({
        url: '/auth/me',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const { useGetUserQuery, useLoginMutation, useExchangeTokenMutation, useLogoutMutation } = authApiSlice;
