export interface UserInfo {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  username: string;
  photourl?: string;
  gender?: string;
  designation?: string;
  dob?: string;
}

export interface UserTenant {
  id: string;
  tenantId: string;
  roleId: string;
  status: string | number;
  user: UserInfo;
}

export interface SubAdmin {
  id: string;
  adminId: string;
  createdOn: string;
  modifiedOn?: string;
  status: string | number;
  userTenant: UserTenant;
  permissions?: Permission[];
  preSignedPhotoUrl?: string;
}

export interface SubAdminFormValues {
  adminId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  designation?: string;
  dob?: string | null;
  gender: string;
  photoUrl?: string;
  permissions: Permission[];
  status?: string | number;
  preSignedPhotoUrl?: string;
}

export interface Permission {
  id: string;
  userTenantId: string;
  permission: string;
  allowed: boolean;
}

export interface Faq {
  id?: string;
  question: string;
  answer: string;
  category: string;
  priority: number;
  visibility?: number;
  status?: number;
}

export interface Notifications {
  id?: string;
  subject: string;
  body: string;
  type?: number;
  receiver: string[] | Subscriber[];
  isDraft: boolean;
}

export interface Subscriber {
  id: string;
  name?: string;
  [key: string]: any;
}
export interface Receiver {
  to: Subscriber[];
}

export interface AdminForm {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  designation: string;
  dob: string;
  gender: string;
  permissions: string[];
  photoUrl?: File | null;
}
export interface HelpItem {
  question: string;
  answer: string;
  category: string;
  visibility: number;
  status: number;
}
export interface SupportContactInfo {
  id: string;
  supportEmail: string;
  supportPhone: string;
  status: number;
  visibility: number;
}
