{"name": "ecomdukescustomermobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-picker/picker": "^2.11.0", "@react-native-seoul/masonry-list": "^1.4.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.10", "@reduxjs/toolkit": "^2.6.1", "@types/draftjs-to-html": "^0.8.4", "@types/react-redux": "^7.1.34", "cashfree-pg-api-contract": "^2.0.7", "country-state-city": "^3.2.1", "draftjs-to-html": "^0.9.1", "formik": "^2.4.6", "jotai": "^2.12.4", "lucide-react-native": "^0.511.0", "react": "19.0.0", "react-native": "0.78.1", "react-native-cashfree-pg-sdk": "^2.2.2", "react-native-device-info": "^14.0.4", "react-native-drawer-layout": "^4.1.11", "react-native-element-dropdown": "^2.12.4", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.27.1", "react-native-gifted-chat": "^2.8.1", "react-native-image-picker": "^8.2.1", "react-native-keyboard-controller": "^1.17.5", "react-native-masonry-list": "^2.16.2", "react-native-mmkv": "^3.2.0", "react-native-paper": "^5.13.1", "react-native-phone-number-input": "^2.1.0", "react-native-picker-select": "^9.3.1", "react-native-progress-steps": "^2.0.3", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.18.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-share": "^12.0.11", "react-native-splash-screen": "^3.3.0", "react-native-star-rating": "^1.1.0", "react-native-star-rating-widget": "^1.9.2", "react-native-step-indicator": "^1.0.3", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.16.1", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^19.1.0", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.1", "@react-native/eslint-config": "0.78.1", "@react-native/metro-config": "0.78.1", "@react-native/typescript-config": "0.78.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}