import React, {useCallback, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {Icon, Text} from 'react-native-paper';
import {colors} from '../../theme/colors';
import customColors from '../../theme/customColors';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {OnboardStackParamList} from '../../navigations/types';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {SCREEN_NAME} from '../../constants/screenNames';
import {useGetUserNotificationsCountQuery} from '../../redux/notification/userNotificationApiSlice';
import {useGetUserQuery} from '../../redux/auth/authApiSlice';
import {useTypedSelector} from '../../redux/store';
type props = {
  chatCount?: number;
  notificationCount?: number;
  coinCount?: number;
};
const NotificationBadge: React.FC<props> = ({
  chatCount = 0,
  notificationCount = 0,
  coinCount = 0,
}) => {
  const navigation =
    useNavigation<NativeStackNavigationProp<OnboardStackParamList>>();
  const [selectedTab, setSelectedTab] = useState<
    'chat' | 'bell' | 'coin' | null
  >(null);
  const {data: user} = useGetUserQuery();
  const userTenantId = user?.userTenantId;
  const isLoggedIn = useTypedSelector(state => state.auth.isLoggedIn);

  const {data: unreadCountData, refetch: refetchUnreadCount} =
    useGetUserNotificationsCountQuery(
      {userTenantId: userTenantId!, isRead: false},
      {
        skip: !userTenantId,
        pollingInterval: 30000, // Poll every 30 seconds for unread count
      },
    );
  useFocusEffect(
    useCallback(() => {
      if (userTenantId) {
        refetchUnreadCount();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userTenantId]),
  );

  const unreadCount = unreadCountData?.count || 0;
  const renderBadge = (count: number) => {
    if (count > 0) {
      return (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{count > 99 ? '99+' : count}</Text>
        </View>
      );
    }
    return null;
  };
  useFocusEffect(
    useCallback(() => {
      if (userTenantId) {
        refetchUnreadCount();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userTenantId]),
  );

  return (
    <View style={styles.container}>
      <View style={styles.iconRow}>
        <TouchableOpacity
          onPress={() => {
            setSelectedTab('chat');
            navigation.navigate(SCREEN_NAME.CHAT_LIST);
          }}
          style={[
            styles.iconButton,
            selectedTab === 'chat' && styles.selectedIconButton,
          ]}>
          <Icon source="chat" size={28} color={colors.tertiary} />
          {renderBadge(chatCount)}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            setSelectedTab('bell');
            navigation.navigate(SCREEN_NAME.NOTIFICATION);
          }}
          style={[
            styles.iconButton,
            selectedTab === 'bell' && styles.selectedIconButton,
          ]}>
          <Icon source="bell" size={28} color={colors.tertiary} />
          {renderBadge(unreadCount || notificationCount)}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            setSelectedTab('coin');
            navigation.navigate(SCREEN_NAME.ECOM_COIN);
          }}
          style={[
            styles.iconButton,
            selectedTab === 'coin' && styles.selectedIconButton,
          ]}>
          <Icon source="hand-coin" size={28} color={colors.tertiary} />
          {renderBadge(coinCount)}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default NotificationBadge;

const styles = StyleSheet.create({
  notificationCount: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: 'red',
    borderRadius: 8,
    width: 14,
    height: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCountText: {
    color: customColors.white,
    fontSize: 8,
    fontWeight: 'bold',
  },
  iconView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  dialogBox: {
    borderRadius: 12,
    backgroundColor: customColors.white,
  },
  header: {fontSize: 16, marginLeft: 10},
  dialogActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },

  iconRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },

  iconButton: {
    marginHorizontal: 4,
  },
  selectedIconButton: {
    padding: 6,
    borderRadius: 20,
    borderColor: '#00004F',
    backgroundColor: 'rgba(0, 0, 79, 0.1)',
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: -4,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
