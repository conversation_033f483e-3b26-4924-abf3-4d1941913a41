// src/utils/getFormFields.ts
import {Dispatch, SetStateAction} from 'react';
import {KeyboardTypeOptions} from 'react-native';

export interface FormField {
  name: string;
  label: string;
  placeholder: string;
  keyboardType?: KeyboardTypeOptions;
  secure?: boolean;
  visibility?: boolean;
  setVisibility?: Dispatch<SetStateAction<boolean>>;
  isPhone?: boolean;
}

export const getFormFields = (
  passwordVisible: boolean,
  setPasswordVisible: Dispatch<SetStateAction<boolean>>,
  confirmPasswordVisible: boolean,
  setConfirmPasswordVisible: Dispatch<SetStateAction<boolean>>,
): FormField[] => [
  {
    name: 'firstName',
    label: '* First Name',
    placeholder: 'First Name',
    keyboardType: 'default',
  },
  {
    name: 'lastName',
    label: '* Last Name',
    placeholder: 'Last Name',
    keyboardType: 'default',
  },
  {
    name: 'phone',
    label: 'Phone Number',
    placeholder: 'Phone',
    keyboardType: 'phone-pad',
    isPhone: true,
  },
  {
    name: 'email',
    label: '* Email',
    placeholder: 'Email',
    keyboardType: 'email-address',
  },
  {
    name: 'password',
    label: '* Password',
    placeholder: 'Password',
    secure: true,
    visibility: passwordVisible,
    setVisibility: setPasswordVisible,
    keyboardType: 'visible-password',
  },
  {
    name: 'confirmPassword',
    label: '* Confirm Password',
    placeholder: 'Confirm password',
    secure: true,
    visibility: confirmPasswordVisible,
    setVisibility: setConfirmPasswordVisible,
    keyboardType: 'visible-password',
  },
  {
    name: 'referralCode',
    label: 'Referral Code (Optional)',
    placeholder: 'Enter referral code',
    keyboardType: 'default',
    secure: false,
  },
];
