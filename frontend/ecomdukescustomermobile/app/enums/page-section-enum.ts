export enum SectionType {
  BANNER = 'banner',
  PRODUCT_LIST = 'product-list',
  CATEGORY_LIST = 'category-list',
  IMAGE_CARDS = 'image-cards',
  OFFER_CARDS = 'offer-cards',
  CATEGORY_CARDS = 'category-cards',
  PRODUCT_CARDS = 'product-cards',
  RECENTLY_VIEWED = 'recently-viewed',
  MOST_VIEWED = 'most-viewed',
  CAROUSEL = 'carousel',
  OCCASION_CARDS = 'occasion-cards',
  FEATURED_COLLECTION = 'featured-collection',
  TEXT_BLOCK = 'text_block',
  FEATURED_PRODUCTS = 'featured_products',
  CATEGORY_GRID = 'category_grid',
  TOP_SELLING = 'top-selling',
  PRODUCT_FILTER = 'product-filter',
  ALL_CATEGORIES = 'all-categories',
  FACETS = 'facets',
  GIFT_PRODUCTS = 'gift-products',
  SIMILAR_PRODUCTS = 'similar-products',
}

export enum SectionItemType {
  PRODUCT = 'product',
  CATEGORY = 'category',
  IMAGE = 'image',
  CUSTOM_CARD = 'custom_card',
  COLLECTION = 'collection',
}

export enum CardStyle {
  BASIC = 'basic',
  IMAGE_ONLY = 'image-only',
  IMAGE_TITLE = 'image-title',
  IMAGE_TITLE_SUBTITLE = 'image-title-subtitle',
  OFFER_CARD = 'offer-card',
  OFFER_CARD_INVERSE = 'offer-card-inverse',
  OCCASION_CARD = 'occasion-card',
  COLLECTION_CARD = 'collection-card',
  PRODUCT_CARD = 'product-card',
  CATEGORY_CARD = 'category-card',
  AVATAR_WITH_TITLE = 'avatar-with-title',
}

export enum PageType {
  HOME = 'home',
  SELL_ON_ECOMDUKES = 'sell-on-ecomdukes',
  ABOUT_US = 'about-us',
  CONTACT_US = 'contact-us',
}

export const PRODUCT_SECTION_TYPES = [
  SectionType.PRODUCT_LIST,
  SectionType.PRODUCT_CARDS,
  SectionType.FEATURED_PRODUCTS,
];
