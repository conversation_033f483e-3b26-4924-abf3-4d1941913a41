export interface ILoginForm {
  username: string;
  password: string;
}

export interface ICredentials extends ILoginForm {
  client_id: string;
}

export interface IKeycloakAuthRedirectUrl {
  redirectUrl: string;
}
export interface ISignupForm {
  firstName: string;
  lastName: string;
  password: string;
  email: string;
  phoneNumber: string;
  referralCode?: string;
}

export interface ITokenResponse {
  code: string;
  accessToken: string;
  refreshToken: string;
  expires: number;
  pubnubToken: string;
}

export interface ITokenExchangeRequest {
  code: string;
}
