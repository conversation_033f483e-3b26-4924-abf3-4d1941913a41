import {Dispatch, SetStateAction} from 'react';
import {KeyboardTypeOptions} from 'react-native';
import {RegisterScreenNavigationProp} from '../../../navigations/types';

export interface FormValues {
  lastName: string;
  firstName: string;
  phone: string;
  email: string;
  password: string;
  confirmPassword: string;
  referralCode?: string;
}

export interface FormField {
  name: string;
  label: string;
  placeholder: string;
  keyboardType?: KeyboardTypeOptions;
  secure?: boolean;
  visibility?: boolean;
  setVisibility?: Dispatch<SetStateAction<boolean>>;
  isPhone?: boolean;
}

export interface ScreenProps {
  navigation: RegisterScreenNavigationProp;
}
