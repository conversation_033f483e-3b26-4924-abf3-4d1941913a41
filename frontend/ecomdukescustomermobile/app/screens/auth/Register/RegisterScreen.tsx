import React, {useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {Divider, HelperText, Text, TextInput} from 'react-native-paper';
import {FormikHelpers, useFormik} from 'formik';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {STRINGS} from '../../../constants/strings';

import CustomButton from '../../../components/CustomButton/CustomButton';
import {colors} from '../../../theme/colors';
import {RegisterScreenNavigationProp} from '../../../navigations/types';
import PhoneInput from 'react-native-phone-number-input';
import Toast from 'react-native-toast-message';
import {FONTS} from '../../../theme/fonts';
import CustomTextInput from '../../../components/InputFields/CustomTextInput';
import {registrationValidationSchema} from '../../../validations/register';

import {
  GoogleSignin,
  isErrorWithCode,
  isSuccessResponse,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {useDispatch} from 'react-redux';
import {
  AuthResData,
  setCredentials,
  setUserDetails,
} from '../../../redux/auth/authSlice';
import {FormValues} from './types';
import {getFormFields} from '../../../constants/register';
import {
  useCreateSignUpTokenMutation,
  useGoogleAuthCallbackMutation,
  useLazyGetUserQuery,
  useSignUpMutation,
} from '../../../redux/auth/authApiSlice';
import {commonStyles, scrollViewProps} from '../../../constants/commonStyles';
import Screenwrapper from '../../../components/ScreenWrapper/ScreenWrapper';

interface ScreenProps {
  navigation: RegisterScreenNavigationProp;
}
const RegisterScreen: React.FC<ScreenProps> = ({navigation}) => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const dispatch = useDispatch();
  const [googleLoginApi] = useGoogleAuthCallbackMutation();
  const [getUserDetails] = useLazyGetUserQuery();
  const [createSignUpToken] = useCreateSignUpTokenMutation();
  const [signUp, {isLoading: signupLoading}] = useSignUpMutation();

  const signInWithGoogle = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      if (isSuccessResponse(response)) {
        const tokens = await GoogleSignin.getTokens();
        const loginResponse = await googleLoginApi({code: tokens.accessToken});
        const token = loginResponse.data;
        if (token) {
          dispatch(setCredentials(token as AuthResData));
        }

        const userDetails = await getUserDetails().unwrap();
        dispatch(setUserDetails(userDetails));
      }
    } catch (error) {
      console.log('signInWithGoogle response', error);

      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.IN_PROGRESS:
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            break;
          default:
        }
      } else {
      }
    }
  };
  const handleRegistration = async (
    values: FormValues,
    actions: FormikHelpers<FormValues>,
  ) => {
    const {resetForm} = actions;
    try {
      const tokenResponse = await createSignUpToken(values.email).unwrap();
      const token = tokenResponse.code;

      const signupData = {
        firstName: values.firstName,
        lastName: values.lastName,
        phoneNumber: values.phone.replace('+', ''),
        email: values.email,
        password: values.password,
        referralCode: values.referralCode || undefined,
      };

      await signUp({token, signupData}).unwrap();

      Toast.show({
        type: 'success',
        text1: 'Registered successfully!',
      });

      resetForm();
      setTimeout(() => {
        navigation.pop();
      }, 1000);
    } catch (error) {}
  };

  const formik = useFormik<FormValues>({
    initialValues: {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      password: '',
      confirmPassword: '',
      referralCode: '',
    },
    validationSchema: registrationValidationSchema,
    enableReinitialize: true,
    onSubmit: (values, actions) => handleRegistration(values, actions),
  });

  const formFields = getFormFields(
    passwordVisible,
    setPasswordVisible,
    confirmPasswordVisible,
    setConfirmPasswordVisible,
  );

  return (
    <Screenwrapper>
      <ScrollView {...scrollViewProps}>
        <View style={commonStyles.mainContainer}>
          <Text variant="titleMedium" style={styles.heading}>
            {STRINGS.registration}
          </Text>

          {formFields.map(field =>
            field.isPhone ? (
              <View key={field.name} style={styles.phoneInputView}>
                <Text variant="bodyMedium" style={styles.phoneNumberTitle}>
                  * {STRINGS.phoneNumber}
                </Text>
                <PhoneInput
                  placeholder=" "
                  defaultValue={formik.values.phone}
                  defaultCode="IN"
                  disableArrowIcon={true}
                  layout="second"
                  onChangeFormattedText={text => {
                    formik.setFieldValue('phone', text);
                    formik.setFieldTouched('phone', true);
                  }}
                  containerStyle={styles.phoneContainer}
                  textContainerStyle={styles.phoneTextContainer}
                  codeTextStyle={styles.codeText}
                  textInputStyle={styles.codeText}
                  flagButtonStyle={styles.flagButton}
                />
                <HelperText
                  style={styles.helperText}
                  type="error"
                  padding="none"
                  visible={
                    !!(
                      formik.touched.phone &&
                      formik.errors.phone &&
                      formik.errors.phone.length > 0
                    )
                  }>
                  {formik.errors.phone}
                </HelperText>
              </View>
            ) : (
              <CustomTextInput
                key={field.name}
                id={field.name}
                title={field.label}
                placeholder={field.placeholder}
                keyboardType={field.keyboardType || 'default'}
                value={formik.values[field.name as keyof FormValues]}
                onChangeText={formik.handleChange(field.name)}
                onBlur={formik.handleBlur(field.name)}
                errors={formik.errors[field.name as keyof FormValues]}
                touched={formik.touched[field.name as keyof FormValues]}
                secureTextEntry={field.secure ? !field.visibility : false}
                right={
                  field.secure ? (
                    <TextInput.Icon
                      icon={field.visibility ? 'eye' : 'eye-off'}
                      onPress={() => field.setVisibility?.(!field.visibility)}
                    />
                  ) : null
                }
              />
            ),
          )}

          <CustomButton
            title="Continue"
            mode="contained"
            contentStyle={styles.ContinueButtonContent}
            style={styles.button}
            onPress={() => formik.handleSubmit()}
            loading={signupLoading}
            disabled={signupLoading}
          />

          <Text style={styles.orText} variant="bodySmall">
            {STRINGS.or}
          </Text>

          <CustomButton
            title={STRINGS.continuewithGoogle}
            mode="outlined"
            icon="google"
            onPress={() => {
              signInWithGoogle();
            }}
            textColor={customColors.outlineButtonTextGrey}
          />
          <CustomButton
            title={STRINGS.continuewithFacebook}
            mode="outlined"
            icon="facebook"
            onPress={() => {}}
            textColor={customColors.outlineButtonTextGrey}
          />
          <Divider style={styles.divider} />
          <CustomButton
            title={STRINGS.haveAnAccount}
            onPress={() => navigation.pop()}
            textColor={customColors.textButtonBlue}
            style={styles.cantLogin}
            mode="text"
          />
        </View>
      </ScrollView>
    </Screenwrapper>
  );
};

export default RegisterScreen;

const styles = StyleSheet.create({
  safeAreaStyle: {
    padding: 0,
  },
  innerContainerStyle: {
    padding: 0,
    margin: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
  },
  mainConatainer: {
    padding: styleConstants.spacing.x20,
    backgroundColor: colors.background,
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  heading: {
    textAlign: 'center',
    marginVertical: '5%',
  },
  ContinueButtonContent: {
    paddingHorizontal: styleConstants.spacing.x20,
  },
  button: {
    marginTop: 20,
    paddingVertical: 5,
    width: 200,
    alignSelf: 'center',
  },
  orText: {alignSelf: 'center', marginTop: styleConstants.spacing.x20},
  divider: {
    marginTop: styleConstants.spacing.x20,
    width: '80%',
    alignSelf: 'center',
    height: 2,
  },
  cantLogin: {
    marginTop: styleConstants.spacing.s10,
  },
  socialIcon: {width: 20, height: 20, marginRight: 8},
  phoneContainer: {
    width: '100%',
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.onPrimary,
    borderWidth: 1,
    borderColor: colors.gray.dark,
  },
  phoneTextContainer: {
    paddingVertical: 0,
    backgroundColor: colors.onPrimary,
    paddingHorizontal: 0,
    paddingLeft: 0,
    borderRadius: 20,
    marginLeft: 0,
  },
  phoneInputView: {width: '100%'},
  phoneNumberTitle: {marginLeft: styleConstants.spacing.x20, marginBottom: 5},
  helperText: {
    marginLeft: 20,
  },
  codeText: {
    fontFamily: FONTS.regular,
    fontSize: 14,
  },
  flagButton: {
    backgroundColor: customColors.borderGrey,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    marginRight: styleConstants.spacing.s10,
    pointerEvents: 'none',
  },
});
