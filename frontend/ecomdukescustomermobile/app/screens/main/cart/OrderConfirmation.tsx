import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import OrderProductCard from './components/OrderProductCard';
import {colors} from '../../../theme/colors';
import {
  useCheckoutCartMutation,
  useGetActiveCartQuery,
  useUpdateCartItemMutation,
  useUpdateCartMutation,
} from '../../../redux/cart/cartApiSlice';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {ActivityIndicator, Icon, Switch, Text} from 'react-native-paper';
import OrderDetailsSection from './components/OrderDetailSection';
import customColors from '../../../theme/customColors';
import CustomButton from '../../../components/CustomButton/CustomButton';
import {OrderConfirmScreenNavigationProp} from '../../../navigations/types';
import {useNavigation} from '@react-navigation/native';
import {CFSession, CFEnvironment} from 'cashfree-pg-api-contract';
import {CFPaymentGatewayService} from 'react-native-cashfree-pg-sdk';
import {usePayment} from '../../../providers/PaymentProvider';
import {PaymentStatus} from '../../../types/payment';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {PromoCode} from '../../../redux/promo/promocode';
import {useLazyGetApplicablePromoCodesQuery} from '../../../redux/promo/promoApiSlice';
import CouponModal from './PromoCodeMoal';
import {fieldsExcludeMetaFields} from '../../../types/filter';
import {useLazyGetProductCustomizationsQuery} from '../../../redux/product/productApiSlice';
import {ProductVariant} from '../../../redux/product/product';
import Toast from 'react-native-toast-message';
import PaymentOptions from './components/PaymentOptions';
import GstInvoiceForm from '../Orders/components/GstInvoiceForm';
import {useGetDukeCoinBalanceQuery} from '../../../redux/ecom/ecomDukesCoinApiSlice';
import {useCheckDiscountEligibilityQuery} from '../../../redux/discount/discountApiSlice';

const OrderConfirmation = ({
  product,
  cartId,
}: {
  product: ProductVariant;
  cartId?: string;
}) => {
  const navigation = useNavigation<OrderConfirmScreenNavigationProp>();
  const {data: user} = useGetUserQuery();
  const [updateCartItem] = useUpdateCartItemMutation();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isCouponModalVisible, setCouponModalVisible] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<PromoCode | null>(null);
  // const [promoDiscount, setPromoDiscount] = useState(0);

  const [selectedAddressId, setSelectedAddressId] = useState<{
    shippingAddressId?: string;
    billingAddressId?: string;
  }>({});
  const [useDukeCoins, setUseDukeCoins] = useState(true);
  const {data: coinData, isLoading: isCoinLoading} =
    useGetDukeCoinBalanceQuery();

  const [gstInfo, setGstInfo] = useState<{
    enabled: boolean;
    gstNumber: string;
    businessName: string;
    businessAddress: string;
  }>({
    enabled: false,
    gstNumber: '',
    businessName: '',
    businessAddress: '',
  });
  type PaymentType = 'upi' | 'card' | 'cod';
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<PaymentType>('upi');

  const [updateCart] = useUpdateCartMutation();
  const [, setProductCustomizations] = useState({});
  const [orderId, setOrderId] = useState<string | undefined>();

  const handleCouponSelect = (coupon: PromoCode) => {
    setAppliedCoupon({
      ...coupon,
      discount: coupon.discount ?? coupon.value,
    });
    setAppliedCoupon(coupon);
    if (activeCart?.id) {
      applyPromoCode(coupon.id, activeCart?.id);
    }
  };

  const handleCartClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };
  const handleAddressClick = () => {
    navigation.navigate(SCREEN_NAME.ADDRESS);
  };
  const handleEditAddressClick = () => {
    navigation.navigate(SCREEN_NAME.ADDRESS);
  };
  const {paymentCallback, setPaymentCallback} = usePayment();

  const debounceTimers = useRef<{[productVariantId: string]: NodeJS.Timeout}>(
    {},
  );
  const [updatingVariantId, setUpdatingVariantId] = useState<string | null>(
    null,
  );

  const [checkoutCart, {isLoading}] = useCheckoutCartMutation();
  const currencyCodeSymbolMap: Map<string, string> = new Map([
    ['INR', '₹'],
    ['USD', '$'],
  ]);
  const [triggerGetCustomizations] = useLazyGetProductCustomizationsQuery();
  useEffect(() => {
    if (!product?.id) {
      return;
    }

    const fetchCustomizationFields = async () => {
      let res = await triggerGetCustomizations({
        where: {
          productVariantId: product.id,
        },
        fields: fieldsExcludeMetaFields,
        include: [
          {
            relation: 'productCustomizationOptions',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
          {
            relation: 'customizationValue',
            scope: {
              fields: fieldsExcludeMetaFields,
              where: {
                cartId,
              },
            },
          },
        ],
      }).unwrap();

      // Fallback to productId if res is empty
      if (!res.length && product.product?.id) {
        res = await triggerGetCustomizations({
          where: {
            productId: product.product.id,
          },
          fields: fieldsExcludeMetaFields,
          include: [
            {
              relation: 'productCustomizationOptions',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
            {
              relation: 'customizationValue',
              scope: {
                fields: fieldsExcludeMetaFields,
                where: {
                  cartId,
                },
              },
            },
          ],
        }).unwrap();
      }

      // Now process whichever response we have
      if (Array.isArray(res) && res.length) {
        const customizationValues: Record<string, string | boolean> = {};

        for (const field of res) {
          const value = field.customizationValue?.value;

          if (value !== undefined && value !== null) {
            const parsedValue =
              field.fieldType === 'checkbox'
                ? value.toString() === 'true'
                : value;

            customizationValues[field.name] = parsedValue;
          }
        }

        setProductCustomizations(prev => ({
          ...prev,
          [product.id]: customizationValues,
        }));
      }
    };

    fetchCustomizationFields();
  }, [cartId, product, triggerGetCustomizations]);

  const {
    data: activeCart,
    isLoading: cartLoading,
    refetch: refetchCarts,
  } = useGetActiveCartQuery({
    filter: {
      include: [
        {
          relation: 'cartItems',
          fields: fieldsExcludeMetaFields,
          scope: {
            where: {deleted: false},
            fields: fieldsExcludeMetaFields,
            include: [
              {
                relation: 'productVariant',
                scope: {
                  fields: fieldsExcludeMetaFields,
                  include: [
                    {
                      relation: 'productVariantPrice',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                    {
                      relation: 'product',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                    {
                      relation: 'featuredAsset',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          relation: 'promoCode',
        },
      ],
    },
  });

  const cartItems = activeCart?.cartItems ?? [];

  const itemTotal = cartItems.reduce((sum, item) => {
    const mrp = parseFloat(
      item.productVariant?.productVariantPrice?.mrp || '0',
    );
    return sum + mrp * item.quantity;
  }, 0);

  const subTotal = cartItems.reduce((sum, item) => {
    const price = parseFloat(
      item.productVariant?.productVariantPrice?.price || '0',
    );
    return sum + price * item.quantity;
  }, 0);
  const [
    getApplicablePromoCodes,
    {data: promoCodes = [], isLoading: isPromoLoading},
  ] = useLazyGetApplicablePromoCodesQuery();

  useEffect(() => {
    if (subTotal > 0) {
      getApplicablePromoCodes({cartValue: subTotal});
    }
  }, [subTotal, getApplicablePromoCodes]);
  useEffect(() => {
    if (activeCart?.promoCodeId && promoCodes.length > 0) {
      const appliedPromo = promoCodes.find(
        p => p.id === activeCart?.promoCodeId,
      );
      if (appliedPromo) {
        setAppliedCoupon(appliedPromo);
        // setPromoDiscount(appliedPromo.value);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeCart?.promoCodeId, promoCodes]);

  const {data: discountEligibility} = useCheckDiscountEligibilityQuery(
    {
      cartTotal: subTotal,
      isFromApp: true,
    },
    {
      skip: subTotal <= 0,
    },
  );

  const dukeCoinDiscount =
    !isCoinLoading && useDukeCoins ? Number(coinData?.maxApplicable ?? 0) : 0;

  const shopDiscount = itemTotal - subTotal;

  const specialDiscount =
    discountEligibility?.appliedDiscount?.discountType === 'PERCENT'
      ? (Number(discountEligibility.appliedDiscount.discountValue) / 100) *
        subTotal
      : Number(discountEligibility?.appliedDiscount?.discountValue) || 0;

  const giftWrap = 0;
  const deliveryCharge = 0;

  const promoDiscountAmount =
    appliedCoupon?.type === 'percentage'
      ? (appliedCoupon.value / 100) * subTotal
      : appliedCoupon?.value || 0;

  const rawTotal =
    subTotal +
    deliveryCharge +
    giftWrap -
    specialDiscount -
    promoDiscountAmount -
    dukeCoinDiscount;

  const totalPayable = Math.max(rawTotal, 0);

  useEffect(() => {
    refetchCarts();
  }, [refetchCarts]);
  useEffect(() => {
    if (user?.profileId) {
      refetchCarts();
    }
  }, [refetchCarts, user?.profileId]);

  const debouncedQuantityChange = (
    item: {id: string; productVariantId: string},
    newQuantity: number,
  ) => {
    const id = item.productVariantId;
    setIsUpdating(true);
    if (debounceTimers.current[id]) {
      clearTimeout(debounceTimers.current[id]);
    }

    debounceTimers.current[id] = setTimeout(() => {
      handleQuantityChange(item, newQuantity);
    }, 500);
  };

  const handleQuantityChange = (
    item: {id: string; productVariantId: string},
    newQuantity: number,
  ) => {
    setIsUpdating(true);
    setUpdatingVariantId(item.productVariantId);
    updateCartItem({
      productVariantId: item.productVariantId,
      quantity: newQuantity,
    })
      .unwrap()
      .finally(() => {
        setTimeout(() => {
          setUpdatingVariantId(null);
          setIsUpdating(false);
        }, 300);
        refetchCarts();
      });
  };
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const applyPromoCode = async (promoCodeId: string, cartId: string) => {
    await updateCart({
      id: cartId,
      data: {promoCodeId},
    }).unwrap();
  };

  const handleCheckout = async () => {
    const shippingId = selectedAddressId.shippingAddressId;
    const billingId = selectedAddressId.billingAddressId;

    if (!shippingId || !billingId) {
      Toast.show({
        type: 'error',
        text1: 'Please select both shipping and billing addresses',
      });
      return;
    }

    if (gstInfo.enabled) {
      const {gstNumber, businessName, businessAddress} = gstInfo;
      if (!gstNumber || !businessName || !businessAddress) {
        Toast.show({
          type: 'error',
          text1: 'Please fill all GST invoice fields.',
        });
        return;
      }
    }

    const payload: any = {
      shippingAddressId: shippingId,
      billingAddressId: billingId,
      promoCodeId: appliedCoupon?.id,
      ...(gstInfo.enabled && {
        gstNumber: gstInfo.gstNumber,
        businessName: gstInfo.businessName,
        businessAddress: gstInfo.businessAddress,
      }),
    };

    if (gstInfo.enabled) {
      payload.gstNumber = gstInfo.gstNumber;
      payload.businessName = gstInfo.businessName;
      payload.businessAddress = gstInfo.businessAddress;
    }
    const discountConditionId =
      discountEligibility?.appliedDiscount?.conditionId;
    const response = await checkoutCart({
      shippingAddressId: shippingId,
      billingAddressId: billingId,
      paymentMethod: selectedPaymentMethod,
      discountConditionId: discountConditionId || undefined,
      ecomDukeCoinsApplied: useDukeCoins
        ? Number(coinData?.maxApplicable || 0)
        : 0,
      ...(gstInfo.enabled && {
        gstNumber: gstInfo.gstNumber,
        businessName: gstInfo.businessName,
        businessAddress: gstInfo.businessAddress,
        promoCodeId: appliedCoupon?.id,
      }),
    }).unwrap();

    await refetchCarts();
    const payment_session_id = response?.payment_session_id;
    const order_id = response?.order_id;

    const session = new CFSession(
      payment_session_id,
      order_id,
      CFEnvironment.SANDBOX,
    );
    CFPaymentGatewayService.doWebPayment(session);
    setOrderId(order_id);
  };

  const handleAddressSelect = (shippingId: string, billingId: string) => {
    setSelectedAddressId({
      shippingAddressId: shippingId,
      billingAddressId: billingId,
    });
  };
  useEffect(() => {
    if (!paymentCallback) {
      return;
    }

    if (paymentCallback.status === PaymentStatus.SUCCESS) {
      setPaymentCallback(null);
      navigation.navigate(SCREEN_NAME.ORDER_SUCCESS, {
        orderId: orderId,
      });
    } else if (paymentCallback.status === PaymentStatus.FAILED) {
      setPaymentCallback(null);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentCallback]);

  if (!cartLoading && cartItems.length === 0) {
    return (
      <View style={styles.emptyCartContainer}>
        <Text style={styles.emptyCartText}>Your cart is empty!</Text>
        <CustomButton
          title="Go to Home"
          onPress={() =>
            navigation.navigate('mainHome', {screen: SCREEN_NAME.HOME})
          }
        />
      </View>
    );
  }

  if (cartLoading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color={colors.tertiary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.card}>
          {isUpdating && (
            <View style={styles.overlay}>
              <ActivityIndicator size="large" color={colors.tertiary} />
            </View>
          )}
          <FlatList
            data={cartItems}
            keyExtractor={(item, index) => item.id + index}
            renderItem={({item}) => {
              const variant = item.productVariant;
              const price = variant?.productVariantPrice?.price ?? '0';
              const mrp = variant?.productVariantPrice?.mrp ?? '0';
              const featureAsset = variant?.featuredAsset.previewUrl;

              return (
                <OrderProductCard
                  productId={item.productVariant?.product.id ?? ''}
                  productVariantId={item.productVariant?.id ?? ''}
                  image={featureAsset}
                  title={
                    (variant?.name ?? 'No Title').slice(0, 20) +
                    ((variant?.name ?? '').length > 20 ? '...' : '')
                  }
                  subtitle={item.productVariant?.product.description || ''}
                  price={`${currencyCodeSymbolMap.get('INR') || ''}${price}`}
                  mrp={`${currencyCodeSymbolMap.get('INR') || ''}${mrp}`}
                  quantity={item.quantity.toString()}
                  onAdd={newQty => debouncedQuantityChange(item, newQty)}
                  onRemove={newQty => debouncedQuantityChange(item, newQty)}
                  onDelete={() => handleQuantityChange(item, 0)}
                  isLoading={updatingVariantId === item.productVariantId}
                  onPress={() => handleCartClick(item.productVariantId)}
                  cartId={item.cartId}
                />
              );
            }}
          />
        </View>
        <PaymentOptions
          selectedPayment={selectedPaymentMethod}
          onChange={setSelectedPaymentMethod}
        />

        {!isCoinLoading && Number(coinData?.maxApplicable) > 0 && (
          <View style={styles.coinRow}>
            <Text style={styles.coinText}>
              Use Duke Coins - ₹{Number(coinData?.maxApplicable).toFixed(2)} off
            </Text>
            <Switch value={useDukeCoins} onValueChange={setUseDukeCoins} />
          </View>
        )}

        {cartLoading || (cartItems.length === 0 && <ActivityIndicator />)}
        <View style={styles.card}>
          <View style={styles.summaryBlock}>
            {[
              ['Item(s) Total [MRP]', itemTotal],
              ['Shop Discount', shopDiscount],
              ['Sub Total', subTotal],
              ['Special Discount', specialDiscount],
              ...(appliedCoupon ? [['Promo Code', promoDiscountAmount]] : []),
              ['Gift Wrap', giftWrap],
            ].map(([label, value], i) => (
              <View key={i} style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>{label}</Text>
                <Text style={styles.summaryValue}>
                  {label === 'Promo Code' ? '-' : ''}
                  {currencyCodeSymbolMap.get('INR') || '₹'}
                  {value}
                </Text>
              </View>
            ))}

            <View style={styles.deliveryCharge}>
              <Text style={styles.estimatedLabel}>Delivery Charge</Text>
              <Text style={styles.estimatedValue}>
                {currencyCodeSymbolMap.get('INR') || '₹'}
                {deliveryCharge.toFixed(2)}
              </Text>
            </View>

            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total Payable</Text>
              <Text style={styles.totalValue}>
                {currencyCodeSymbolMap.get('INR') || '₹'}
                {totalPayable.toFixed(2)}
              </Text>
            </View>
          </View>
          {appliedCoupon && promoDiscountAmount > 0 && (
            <View style={styles.appliedCouponContainer}>
              <Icon
                source="check-circle"
                size={20}
                color={customColors.green}
              />
              <Text style={styles.appliedCouponMessage}>
                Coupon {appliedCoupon.code} applied. You saved ₹
                {promoDiscountAmount.toFixed(2)}.
              </Text>
            </View>
          )}
        </View>

        <GstInvoiceForm onChange={setGstInfo} />
        <View style={styles.card}>
          <OrderDetailsSection
            hanldePress={handleAddressClick}
            handleEditAddress={handleEditAddressClick}
            onAddressSelect={handleAddressSelect}
          />
        </View>
        <View style={styles.couponRow}>
          <TouchableOpacity
            style={styles.couponButton}
            onPress={() => setCouponModalVisible(true)}
            activeOpacity={0.8}>
            <Icon source="tag" size={20} color={colors.tertiary} />

            <View style={styles.textWrapper}>
              <Text style={styles.buttonText}>
                {appliedCoupon ? 'Coupon Applied' : 'Apply Coupon'}
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => {
                setAppliedCoupon(null);
              }}
              style={styles.rightIconWrapper}>
              <Icon source="trash-can" size={20} color={colors.tertiary} />
            </TouchableOpacity>
          </TouchableOpacity>

          <CouponModal
            visible={isCouponModalVisible}
            onClose={() => setCouponModalVisible(false)}
            onSelect={handleCouponSelect}
            promoCodes={promoCodes.map(code => ({
              ...code,
              discount: code.value,
            }))}
            loading={isPromoLoading}
            refetch={refetchCarts}
            activePromo={
              appliedCoupon
                ? {
                    ...appliedCoupon,
                    type: appliedCoupon.type ?? 'flat',
                  }
                : undefined
            }
          />
        </View>

        <CustomButton
          disabled={!selectedAddressId}
          title="Proceed To Checkout"
          onPress={handleCheckout}
          loading={isLoading}
        />
      </ScrollView>
    </View>
  );
};

export default OrderConfirmation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray.backGround,
    padding: 10,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255,255,255,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  content: {
    padding: 10,
    backgroundColor: colors.gray.backGround,
    borderRadius: 10,
  },
  block: {
    marginBottom: 24,
  },
  paymentButtonRow: {
    flexDirection: 'row',
    gap: 20,
    alignItems: 'center',
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },

  option: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginBottom: 10,
    backgroundColor: colors.gray.backGround,
  },

  summaryBlock: {
    backgroundColor: colors.background,
    padding: 12,
    borderRadius: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  summaryLabel: {
    fontSize: 13,
    color: customColors.textBlack,
  },
  summaryValue: {
    fontSize: 13,
    fontWeight: '600',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: colors.gray.medium,
  },
  totalLabel: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  totalValue: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  estimatedDelivery: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  deliveryCharge: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  estimatedLabel: {
    color: customColors.textBlack,
    fontSize: 13,
  },
  estimatedValue: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  sectionHeader: {
    fontSize: 13,
    fontWeight: 'bold',
    color: colors.tertiary,
    marginBottom: 8,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray.medium,
    marginBottom: 12,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  text: {
    flex: 1,
    textAlign: 'center',
    color: colors.tertiary,
    fontWeight: '500',
  },
  addCoupon: {
    flexDirection: 'row',
    marginTop: 20,
    borderRadius: 20,
    padding: 12,
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.gray.backGround,
  },
  emptyCartText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: customColors.textBlack,
  },

  paymentInput: {
    marginTop: 10,
    width: 300,
    height: 50,
  },
  paymentIcon: {
    width: 124,
    height: 24,
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray.backGround,
  },
  paymentItems: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  couponButtonContainer: {
    flex: 1,
    marginHorizontal: 10,
  },
  appliedCouponContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: customColors.lightGreen,
    paddingVertical: 8,
    paddingHorizontal: 6,
    borderRadius: 6,
    marginTop: 12,
  },

  appliedCouponMessage: {
    fontSize: 14,
    color: customColors.green,
    fontWeight: '500',
    marginLeft: 18,
    marginRight: 10,
    marginBottom: 8,
    marginTop: 8,
  },

  couponRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
  },

  couponButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.tertiary,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 10,
    width: '100%',
  },

  textWrapper: {
    flex: 1,
    alignItems: 'center',
  },

  buttonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.tertiary,
  },

  rightIconWrapper: {
    paddingLeft: 8,
  },
  coinRow: {
    padding: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
    backgroundColor: customColors.white,
    borderRadius: 8,
  },
  coinText: {
    fontSize: 14,
    marginLeft: 8,
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
  greenText: {
    color: customColors.green,
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
  },

  primaryText: {
    color: colors.primary,
    fontSize: 14,
    marginTop: 4,
  },
});
