import React, {useEffect, useState} from 'react';
import {
  Modal,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {Icon} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {PromoCode} from '../../../types/cartApi';
import CustomButton from '../../../components/CustomButton/CustomButton';

// export type PromoCode = {
//   id: string;
//   code: string;
//   discount: number;
//   value: number;
//   validFrom: string;
//   validTill: string;
// };

type CouponModalProps = {
  visible: boolean;
  onClose: () => void;
  onSelect: (coupon: PromoCode) => void;
  promoCodes: PromoCode[];
  loading: boolean;
  refetch: () => void;
  activePromo?: PromoCode | null;
};

const CouponModal: React.FC<CouponModalProps> = ({
  visible,
  onClose,
  onSelect,
  promoCodes,
  loading,
  refetch,
  activePromo,
}) => {
  const [selectedPromo, setSelectedPromo] = useState<PromoCode | null>(
    activePromo || null,
  );
  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

  useEffect(() => {
    setSelectedPromo(activePromo || null);
  }, [activePromo]);

  const handleApply = () => {
    if (selectedPromo) {
      onSelect(selectedPromo);
      refetch();
      onClose();
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Available Coupons</Text>

          {loading ? (
            <ActivityIndicator size="large" />
          ) : (
            <FlatList
              data={promoCodes}
              keyExtractor={item => item.code}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={[
                    styles.promoItem,
                    activePromo?.code === item.code && styles.promoItemSelected,
                  ]}
                  onPress={() => onSelect(item)}>
                  <View style={styles.promoLeft}>
                    {activePromo?.code === item.code ? (
                      <Icon
                        source="check-circle"
                        size={20}
                        color={colors.primary}
                      />
                    ) : (
                      <Icon
                        source="circle-outline"
                        size={20}
                        color={colors.gray.medium}
                      />
                    )}
                  </View>

                  <View style={styles.promoContent}>
                    <Text style={styles.promoCodeText}>{item.code}</Text>
                    <Text style={styles.promoDescriptionText}>
                      Save{' '}
                      {item.type === 'percentage'
                        ? `${item.value}%`
                        : `₹${item.value}`}{' '}
                      {item.minCartValue &&
                        `on min purchase of ₹${item.minCartValue}`}
                    </Text>
                    <Text style={styles.promoExpiryText}>
                      Expires on: {formatDate(item.validTill)}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
            />
          )}

          <View style={styles.actionRow}>
            <CustomButton title={'Close'} onPress={onClose} />
            <CustomButton
              title={'Apply'}
              onPress={handleApply}
              disabled={!selectedPromo}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default CouponModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: colors.transparent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '70%',
    backgroundColor: customColors.white,
    borderRadius: 10,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  promoItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: colors.gray.light,
  },
  promoText: {
    fontSize: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  applyButton: {
    flex: 1,
    marginRight: 6,
  },
  removeButton: {
    flex: 1,
    marginLeft: 6,
  },
  selectedPromoItem: {
    borderColor: colors.primary,
    backgroundColor: '#f0f8ff',
  },
  headRow: {flexDirection: 'row', justifyContent: 'space-between'},
  promoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  promoTextWrapper: {
    flex: 1,
  },
  promoCodeText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  promoDetailText: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  promoExpiryText: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#000',
    marginHorizontal: 5,
    alignItems: 'center',
    borderRadius: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },

  promoItemSelected: {
    backgroundColor: '#f0f0f0',
  },

  promoLeft: {
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },

  promoContent: {
    flex: 1,
    paddingLeft: 10,
  },

  promoDescriptionText: {
    fontSize: 14,
    color: '#555',
  },
});
