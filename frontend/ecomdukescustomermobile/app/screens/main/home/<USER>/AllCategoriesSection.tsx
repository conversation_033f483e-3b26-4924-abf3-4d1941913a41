import React, {useEffect, useMemo, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  Pressable,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {Chip} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {useGetCollectionsQuery} from '../../../../redux/collection/collectionApiSlice';
import {IFilter} from '../../../../types/api';
import ProductCard from '../../Products/Components/ProductCard';
import {useGetProductVariantsQuery} from '../../../../redux/product/productApiSlice';
import {viewAllGiftNavigationProp} from '../../../../navigations/types';
import CustomButton from '../../../../components/CustomButton/CustomButton';
import {SCREEN_NAME} from '../../../../constants/screenNames';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
import {ProductVariant} from '../../../../redux/product/product';

const getVisibleCount = () => {
  const w = Dimensions.get('window').width;
  if (w < 380) return 2;
  if (w < 480) return 3;
  if (w < 600) return 4;
  if (w < 720) return 5;
  return 6;
};

const CARD_GUTTER = 13;

const AllCategoriesSection: React.FC = () => {
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);
  const {data: collections = [], isLoading: isCollectionsLoading} =
    useGetCollectionsQuery({fields: {id: true, name: true}});

  useEffect(() => {
    if (collections.length && !selectedCollectionId) {
      setSelectedCollectionId(collections[0].id);
    }
  }, [collections, selectedCollectionId]);

  const productVariantFilter: IFilter | undefined = selectedCollectionId
    ? {
        include: [
          {
            relation: 'product',
            required: true,
            scope: {where: {collectionId: selectedCollectionId}},
          },
          {relation: 'productVariantPrice'},
          {relation: 'featuredAsset'},
        ],
        limit: 8,
      }
    : undefined;

  const {data: productVariants = [], isLoading: isVariantsLoading} =
    useGetProductVariantsQuery(productVariantFilter || {});

  const visibleCount = useMemo(getVisibleCount, []);
  const {visibleCollections, remainingCollectionsCount} = useMemo(() => {
    const visible = collections.slice(0, visibleCount);
    return {
      visibleCollections: visible,
      remainingCollectionsCount: collections.length - visible.length,
    };
  }, [collections, visibleCount]);

  const navigation = useNavigation<viewAllGiftNavigationProp>();
  const getDiscountPercentage = (mrp?: number, price?: number): string => {
    if (!mrp || !price || mrp <= price) return '';
    const discount = ((mrp - price) / mrp) * 100;
    return `${Math.round(discount)}`;
  };
  const handleProductClick = (product: ProductVariant) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId: product.id});
  };
  const renderCollectionChip = ({item}: any) => {
    const isSelected = selectedCollectionId === item.id;
    return (
      <Pressable
        style={[
          styles.collectionBtn,
          isSelected && styles.collectionBtnSelected,
        ]}
        android_ripple={{color: '#DDD'}}
        onPress={() => setSelectedCollectionId(item.id)}>
        <Text
          style={[
            styles.collectionText,
            isSelected && {color: customColors.white},
          ]}>
          {item.name}
        </Text>
      </Pressable>
    );
  };

  return (
    <View>
      <View style={styles.headerRow}>
        <View
          style={{
            flex: 1,
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <Text style={styles.sectionTitle}>All Categories</Text>
          <CustomButton
            mode={'outlined'}
            title={'View more'}
            onPress={() =>
              navigation.navigate(SCREEN_NAME.VIEW_ALL_PRODUCT, {
                type: 'all-categories',
                title: 'All Categories',
                allCollections: collections,
              })
            }
            labelStyle={styles.categoryLabel}
          />
        </View>
      </View>

      {isCollectionsLoading ? (
        <ActivityIndicator size="small" style={{marginVertical: 8}} />
      ) : (
        <FlatList
          data={visibleCollections}
          keyExtractor={item => item.id}
          renderItem={renderCollectionChip}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            paddingHorizontal: 16,
            gap: 6,
            marginBottom: 10,
          }}
          ListFooterComponent={
            remainingCollectionsCount > 0 ? (
              <Chip
                textStyle={{color: customColors.white}}
                style={{
                  marginHorizontal: 4,
                  backgroundColor: customColors.primaryContainer,
                }}
                compact
                onPress={() =>
                  navigation.navigate(SCREEN_NAME.VIEW_ALL_PRODUCT, {
                    type: 'all-categories',
                    title: 'All Categories',
                    allCollections: collections,
                  })
                }>
                +{remainingCollectionsCount} more
              </Chip>
            ) : null
          }
        />
      )}

      {isVariantsLoading ? (
        <ActivityIndicator size="large" style={{marginTop: 24}} />
      ) : (
        <FlatList
          data={productVariants.slice(0, 8)}
          keyExtractor={item => item.id}
          numColumns={2}
          renderItem={({item}) => (
            <ProductCard
              isloading={isVariantsLoading}
              id={item.id}
              name={item.name}
              shortDescription={item.product?.description}
              image={item.featuredAsset?.previewUrl}
              price={item.productVariantPrice?.price}
              originalPrice={item.productVariantPrice?.mrp}
              discount={getDiscountPercentage(
                Number(item.productVariantPrice?.mrp),
                Number(item.productVariantPrice?.price),
              )}
              rating={item.product?.rating}
              ratingCount={item.reviews?.length}
              isWishlisted={item.wishlist ? true : false}
              onPress={() => handleProductClick(item)}
              onAddToCart={() => {}}
              onGoToCart={() => {}}
              onWishlistToggle={() => {}}
            />
          )}
          columnWrapperStyle={{gap: CARD_GUTTER, marginBottom: CARD_GUTTER}}
          contentContainerStyle={{
            paddingHorizontal: CARD_GUTTER,
            paddingBottom: 24,
          }}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

export default AllCategoriesSection;

const styles = StyleSheet.create({
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    marginLeft: 15,
    fontSize: 16,
    fontWeight: '600',
    color: colors.tertiary,
  },

  collectionBtn: {
    borderWidth: 1,
    borderColor: colors.gray.medium,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
    backgroundColor: customColors.white,
  },
  collectionBtnSelected: {
    backgroundColor: customColors.primaryContainer,
    borderColor: customColors.white,
  },
  collectionText: {fontSize: 14, color: customColors.textBlack},
  cardWrapper: {marginBottom: CARD_GUTTER},
  categoryLabel: {color: customColors.textBlack},
});
