import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, ScrollView, Dimensions} from 'react-native';
import {<PERSON><PERSON>, Searchbar} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {HomeScreenNavigationProp} from '../../../navigations/types';
import {useNavigation} from '@react-navigation/native';
import SubCategoryList from '../Products/Components/SubCategoryList';
import CustomButton from '../../../components/CustomButton/CustomButton';
import VersionChecker from '../../../components/versionChecker/VersionChecker';
import {
  useGetMostViewedProductsQuery,
  useGetPageSectionsQuery,
  useGetRecentlyViewedProductsQuery,
  useGetTopSellingProductsQuery,
  useLazyGetSimilarProductsQuery,
} from '../../../redux/pageSection/pageSectionApiSlice';
import {ReviewStatus} from '../../../redux/product/product';
import {fieldsExcludeMetaFields} from '../../../types/api';
import {useSelector} from 'react-redux';
import {RootState, useTypedSelector} from '../../../redux/store';
import {
  CardStyle,
  PageType,
  SectionType,
} from '../../../enums/page-section-enum';
import {PageSection} from '../../../types/page-section';
import {CarouselComponent} from './components/CarouselComponent';
import {Banner} from './components/Banner';
import {ProductList} from './components/ProductList';
import OfferList from './components/OfferList';
import {Images} from '../../../assets/images';
import GiftProductsSection from './components/GiftProductSection';
import AllCategoriesSection from './components/AllCategoriesSection';
import FullScreenLoader from '../../../components/FullScreenLoader';

const {width} = Dimensions.get('window');
const HomeScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);
  const user = useSelector((state: RootState) => state.auth.userDetails);

  const {data: sections = [], isLoading: sectionLoading} =
    useGetPageSectionsQuery({
      where: {isActive: true, pageType: PageType.HOME},
      order: ['displayOrder ASC'],
      include: [
        {relation: 'sectionItems', scope: {fields: fieldsExcludeMetaFields}},
      ],
      fields: fieldsExcludeMetaFields,
    });

  const productFilter = React.useMemo(() => {
    return {
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: {preview: true, id: true},
          },
        },
        {
          relation: 'product',
          scope: {
            fields: {description: true, id: true},
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {
              price: true,
              mrp: true,
              currencyCode: true,
            },
          },
        },
        ...(isLoggedIn
          ? [
              {
                relation: 'wishlist',
                scope: {
                  where: {
                    deleted: false,
                    customerId: user?.profileId,
                  },
                  fields: {id: true},
                },
              },
            ]
          : []),
        {
          relation: 'reviews',
          scope: {
            fields: {
              rating: true,
            },
            where: {
              status: ReviewStatus.APPROVED,
            },
          },
        },
      ],
      fields: {
        name: true,
        id: true,
        featuredAssetId: true,
        productId: true,
      },
    };
  }, [isLoggedIn, user?.profileId]);

  const shouldFetchMostViewed = React.useMemo(
    () => sections.some(s => s.type === SectionType.MOST_VIEWED),
    [sections],
  );

  const shouldFetchRecentlyViewed = React.useMemo(
    () => sections.some(s => s.type === SectionType.RECENTLY_VIEWED),
    [sections],
  );

  const shouldFetchTopSelling = React.useMemo(
    () => sections.some(s => s.type === SectionType.TOP_SELLING),
    [sections],
  );

  const {data: mostViewed = [], isLoading: loadMostViewed} =
    useGetMostViewedProductsQuery(productFilter, {
      skip: !isLoggedIn || !shouldFetchMostViewed,
    });

  const {data: recentlyViewed = [], isLoading: loadRecentViewed} =
    useGetRecentlyViewedProductsQuery(productFilter, {
      skip: !isLoggedIn || !shouldFetchRecentlyViewed,
    });

  const {data: topSelling = [], isLoading: loadTopselling} =
    useGetTopSellingProductsQuery(productFilter, {
      skip: !shouldFetchTopSelling,
    });
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSubmitSearch = (searchQuery: string) => {
    if (searchQuery.trim() !== '') {
      navigation.navigate(SCREEN_NAME.SUGGESTION, {
        searchQuery,
      });
      setSearchQuery('');
    }
  };
  const [fetchSimilarProducts, {data = [], isLoading: similarProductsLoading}] =
    useLazyGetSimilarProductsQuery();

  useEffect(() => {
    fetchSimilarProducts({
      filter: {
        include: [
          {
            relation: 'featuredAsset',
            scope: {
              fields: {preview: true, id: true},
            },
          },
          {
            relation: 'product',
            required: true,
            scope: {
              fields: {description: true, id: true},
            },
          },
          {
            relation: 'productVariantPrice',
            scope: {
              fields: {
                price: true,
                mrp: true,
                currencyCode: true,
              },
            },
          },
          ...(isLoggedIn
            ? [
                {
                  relation: 'wishlist',
                  scope: {
                    where: {
                      deleted: false,
                      customerId: user?.profileId,
                    },
                    fields: {id: true},
                  },
                },
              ]
            : []),
          {
            relation: 'reviews',
            scope: {
              fields: {
                rating: true,
              },
              where: {
                status: ReviewStatus.APPROVED,
              },
            },
          },
        ],
        fields: {
          name: true,
          id: true,
          featuredAssetId: true,
          productId: true,
        },
        limit: 10,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchSimilarProducts]);

  const handleFocus = () => {
    navigation.navigate(SCREEN_NAME.SUGGESTION, {searchQuery: ''});
    setSearchQuery('');
  };
  // const store = useTypedSelector(state => state);

  const renderSectionMobile = (section: PageSection) => {
    switch (section.type) {
      case SectionType.CAROUSEL:
        return (
          <View key={section.id} style={styles.carouselContainer}>
            <CarouselComponent items={section.sectionItems || []} />
          </View>
        );
      case SectionType.BANNER:
        const bannerItem = section.sectionItems?.[0];
        return bannerItem ? (
          <View key={section.id} style={styles.bannerContainer}>
            <Banner item={bannerItem} />
          </View>
        ) : null;
      case SectionType.FEATURED_COLLECTION:
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const topCategories =
          section.sectionItems?.map(item => ({
            id: item.id,
            entityId: item.entityId,
            name: item.title,
            image: item.previewUrl || item.imageUrl,
          })) || [];
        const shouldShowTitle = section.metadata?.showTitle !== false;
        const imagesStyleAvatar =
          section.cardStyle === CardStyle.AVATAR_WITH_TITLE;
        return (
          <View key={section.id} style={{marginBottom: 10}}>
            {shouldShowTitle && (
              <Text style={styles.sectionTitle}>{section.title}</Text>
            )}
            <SubCategoryList
              overlayNames={shouldShowTitle}
              onPress={item =>
                navigation.navigate(SCREEN_NAME.PRODUCT_DETAILS, {
                  collectionId: item.entityId,
                })
              }
              data={topCategories.map(item => ({...item, id: Number(item.id)}))}
              showNames={shouldShowTitle ? false : true}
              imageStyle={
                imagesStyleAvatar ? styles.categoryImage : styles.shopCategory
              }
              imageContainerStyle={
                imagesStyleAvatar
                  ? styles.categoryContainer
                  : styles.shopCategoryContainer
              }
            />
          </View>
        );
      case SectionType.FEATURED_PRODUCTS:
        const featuredVariants = (section.sectionItems ?? []).flatMap(
          item => item.productVariants || [],
        );

        return (
          <View key={section.id} style={styles.productSection}>
            <Text style={styles.sectionTitle}>{section.title}</Text>

            <ProductList title={section.title} variants={featuredVariants} />
          </View>
        );
      case SectionType.RECENTLY_VIEWED:
        if (!isLoggedIn || loadRecentViewed) return null;
        return (
          <View key={section.id}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
            </View>
            <View style={styles.subCategory}>
              <ProductList title={section.title} variants={recentlyViewed} />
            </View>
          </View>
        );
      case SectionType.MOST_VIEWED:
        if (!isLoggedIn || loadMostViewed) return null;
        return (
          <View key={section.id} style={styles.mostViewdContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{section.title}</Text>

              <CustomButton
                mode={'outlined'}
                title={'View more'}
                onPress={() =>
                  navigation.navigate(SCREEN_NAME.VIEW_ALL_PRODUCT, {
                    type: 'most-viewed',
                    title: 'Most Viewed',
                    variants: mostViewed,
                  })
                }
                labelStyle={styles.categoryLabel}
              />
            </View>

            <ProductList title={section.title} variants={mostViewed} />
          </View>
        );
      case SectionType.TOP_SELLING:
        if (loadMostViewed) return null;
        return (
          <View key={section.id} style={styles.topSellingProduct}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <ProductList title={section.title} variants={topSelling} />
          </View>
        );
      case SectionType.GIFT_PRODUCTS:
        return (
          <GiftProductsSection
            key={section.id}
            section={section}
            isLoggedIn={isLoggedIn}
            user={user}
          />
        );

      case SectionType.FACETS:
        const facetItems = section.sectionItems ?? [];
        const mappedData = facetItems.map(item => {
          const hasTitle = !!item.title;
          const hasSubtitle = !!item.subtitle;

          let styleVariant = CardStyle.IMAGE_ONLY;
          if (hasTitle && hasSubtitle) {
            styleVariant = CardStyle.IMAGE_TITLE_SUBTITLE;
          } else if (hasTitle) {
            styleVariant = CardStyle.IMAGE_TITLE;
          }

          return {
            id: item.id,
            title: item.title,
            subtitle: item.subtitle,
            image: item.previewUrl ? {uri: item.previewUrl} : Images.backpack,
            styleVariant,
            facetValueIds: item?.metadata?.facetValueIds,
          };
        });
        return (
          <View key={section.id} style={styles.productSection}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <OfferList
              data={mappedData}
              onPress={item =>
                navigation.navigate(SCREEN_NAME.PRODUCT_DETAILS, {
                  facetValueIds: item.facetValueIds ?? [],
                })
              }
            />
          </View>
        );

      case SectionType.ALL_CATEGORIES:
        return <AllCategoriesSection />;
      case SectionType.SIMILAR_PRODUCTS:
        if (!isLoggedIn) return null;
        return (
          <View key={section.id}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
            </View>
            <View style={styles.subCategory}>
              <ProductList title={section.title} variants={data} />
            </View>
          </View>
        );
      default:
        return null;
    }
  };

  useTypedSelector(state => state.cart.cartList);
  const anyLoading =
    sectionLoading ||
    loadMostViewed ||
    loadRecentViewed ||
    loadTopselling ||
    similarProductsLoading;
  if (anyLoading) {
    return <FullScreenLoader />;
  }
  return (
    <ScrollView style={styles.container}>
      <VersionChecker />

      <Searchbar
        placeholder="Search products..."
        onChangeText={text => {
          setSearchQuery(text);
          onSubmitSearch(text);
        }}
        onFocus={() => handleFocus()}
        onSubmitEditing={() => onSubmitSearch(searchQuery)}
        onIconPress={() => onSubmitSearch(searchQuery)}
        value={searchQuery}
        style={styles.searchbar}
        placeholderTextColor={colors.gray.medium}
      />

      {sections.map(section => renderSectionMobile(section))}

      <Button
        rippleColor="transparent"
        mode={'outlined'}
        onPress={() => navigation.navigate(SCREEN_NAME.EXPLORE_ALL)}
        style={styles.exlporeButton}
        labelStyle={styles.exploreButtonLabel}>
        Explore all products
      </Button>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {padding: 10, backgroundColor: customColors.white},
  flatListContent: {
    paddingHorizontal: 10,
    marginTop: 10,
  },
  flatListContainer: {gap: 10, paddingVertical: 10},

  productCardWidth: {width: 185},
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
  },
  sectionTitle: {
    marginLeft: 15,
    marginBottom: 8,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
  mostViewdContainer: {marginTop: 10},
  mostViewd: {
    marginLeft: 4,
    marginBottom: 8,
    marginTop: 10,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
  viewMore: {
    color: customColors.appBlue,
    fontSize: 14,
    height: 30,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.gray.medium,
    fontWeight: '500',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 1,
  },

  bannerText: {
    color: customColors.textBlack,
  },
  exploreButton: {
    marginTop: 24,
    borderRadius: 25,
    paddingVertical: 8,
    color: customColors.textBlack,
    fontWeight: 'bold',
    fontSize: 18,
  },
  exploreButtonLabel: {
    color: customColors.textBlack,
    fontWeight: 'bold',
    fontSize: 18,
  },
  subCategory: {marginBottom: 0, paddingBottom: 0},
  similarProducts: {
    marginTop: 20,
  },

  imageContainerStyle: {
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
  },
  categoryContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    marginTop: 10,
  },
  categoryImage: {
    borderRadius: 50,
    backgroundColor: customColors.white,
    height: 50,
    width: 50,
  },
  subCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  shopCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  OccationCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
    marginLeft: 10,
  },

  exlporeButton: {
    width: 380,
    alignSelf: 'center',
    backgroundColor: customColors.white,
    borderColor: colors.tertiaryBlue,
    padding: 6,
    borderWidth: 1.5,
    marginBottom: 20,
  },
  categoryLabel: {color: customColors.textBlack},
  shopCategory: {height: 120},
  occasionCategory: {height: 100, width: 120},
  bannerContainer: {marginBottom: 10, paddingBottom: 0, marginTop: 20},
  bannerContainer2: {marginBottom: 0, paddingBottom: 0, marginTop: 20},
  topSellingProduct: {padding: 0},
  carouselImage: {
    width: width,
    height: 180,
    resizeMode: 'cover',
  },
  productSection: {
    marginVertical: 20,
  },
  carouselContainer: {
    width: '100%',
    height: 180,
    marginVertical: 12,
  },
  fullScreenLoader: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray.backGround,
  },
});

export default HomeScreen;
