import React, {useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import {useLazyGetSimilarProductsQuery} from '../../../redux/pageSection/pageSectionApiSlice';
import {ReviewStatus} from '../../../redux/product/product';
import ProductCard from '../Products/Components/ProductCard';

interface SimilarProductsSectionProps {
  productId: string;
  isLoggedIn: boolean;
  userProfileId?: string;
  onProductPress: (id: string) => void;
}

const SimilarProduct: React.FC<SimilarProductsSectionProps> = ({
  productId,
  isLoggedIn,
  userProfileId,
  onProductPress,
}) => {
  const [fetchSimilar, {data = [], isLoading}] =
    useLazyGetSimilarProductsQuery();

  useEffect(() => {
    if (!productId) return;

    fetchSimilar({
      productId,
      filter: {
        where: {productId}, // ← restrict to this product’s similars
        limit: 10,
        include: [
          {
            relation: 'featuredAsset',
            scope: {fields: {preview: true, id: true}},
          },
          {
            relation: 'product',
            required: true,
            scope: {fields: {description: true, id: true}},
          },
          {
            relation: 'productVariantPrice',
            scope: {
              fields: {price: true, mrp: true, currencyCode: true},
            },
          },
          ...(isLoggedIn && userProfileId
            ? [
                {
                  relation: 'wishlist',
                  scope: {
                    where: {
                      deleted: false,
                      customerId: userProfileId,
                    },
                    fields: {id: true},
                  },
                },
              ]
            : []),
          {
            relation: 'reviews',
            scope: {
              where: {status: ReviewStatus.APPROVED},
              fields: {rating: true},
            },
          },
        ],
        fields: {
          id: true,
          name: true,
          featuredAssetId: true,
          productId: true,
        },
      },
    });
  }, [productId, isLoggedIn, userProfileId, fetchSimilar]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Similar Products</Text>
        <ActivityIndicator style={{marginTop: 16}} />
      </View>
    );
  }

  if (data.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Similar Products</Text>
        <Text style={styles.emptyText}>No similar products found.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Similar Products</Text>
      <FlatList
        data={data}
        keyExtractor={item => item.id.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        renderItem={({item}) => (
          <View style={styles.cardWrapper}>
            <ProductCard
              id={item.id}
              image={item.featuredAsset?.previewUrl || ''}
              name={item.name}
              price={item.productVariantPrice?.price?.toString()}
              originalPrice={item.productVariantPrice?.mrp?.toString()}
              discount={
                item.productVariantPrice
                  ? `${Math.round(
                      ((item.productVariantPrice.mrp -
                        item.productVariantPrice.price) /
                        item.productVariantPrice.mrp) *
                        100,
                    )}%`
                  : undefined
              }
              rating={item.reviews?.[0]?.rating || 0}
              shortDescription={item.product?.description}
              isWishlisted={!!item.wishlist?.id}
              onWishlistToggle={() => {}}
              onAddToCart={() => {}}
              onGoToCart={() => {}}
              onPress={() => onProductPress(item.id)}
            />
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    paddingHorizontal: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyText: {
    color: '#666',
    marginTop: 8,
  },
  listContent: {
    paddingLeft: 4,
  },
  cardWrapper: {
    width: 180,
    marginRight: 12,
  },
});

export default SimilarProduct;
