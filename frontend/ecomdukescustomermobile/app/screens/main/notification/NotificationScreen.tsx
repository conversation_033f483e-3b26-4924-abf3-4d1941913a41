import React, {useState} from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Button,
  Modal,
} from 'react-native';
import {Avatar} from 'react-native-paper';
import dayjs from 'dayjs';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {useGetUserNotificationsQuery} from '../../../redux/notification/userNotificationApiSlice';
import {colors} from '../../../theme/colors';
import CustomButton from '../../../components/CustomButton/CustomButton';

const NotificationScreen = () => {
  const {data: user} = useGetUserQuery();
  const userTenantId = user?.userTenantId;
  type NotificationItem = {
    id: string;
    isRead: boolean;
    createdOn: string;
    notification: {
      subject: string;
      body: string;
    };
  };
  const [selectedNotification, setSelectedNotification] =
    useState<NotificationItem | null>(null);

  const [modalVisible, setModalVisible] = useState(false);

  const handleNotificationPress = item => {
    setSelectedNotification(item);
    setModalVisible(true);
  };

  const handleSubscribe = () => {
    // Trigger subscribe logic here (API or local state update)
    setModalVisible(false);
  };

  const handleMarkAsRead = () => {
    // Trigger mark-as-read logic here (API or local state update)
    setModalVisible(false);
  };

  // const [markAllAsRead] = useMarkAllNotificationsAsReadMutation();
  const {data: notifications} = useGetUserNotificationsQuery(
    {userTenantId: userTenantId!, filter: {limit: 50}},
    {skip: !userTenantId},
  );

  // useEffect(() => {
  //   if (userTenantId) {
  //     markAllAsRead(userTenantId)
  //       .unwrap()
  //       .then(() => {
  //         console.log('Marked all as read successfully');
  //       })
  //       .catch(err => {
  //         console.log('Mark all as read error', err);
  //       });
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [userTenantId]);

  const renderItem = ({item}) => {
    const isUnread = !item.isRead;

    return (
      <TouchableOpacity
        onPress={() => handleNotificationPress(item)}
        style={[styles.card, {backgroundColor: isUnread ? '#F6EDF6' : '#fff'}]}>
        <View style={styles.iconContainer}>
          <Avatar.Icon
            icon="bell"
            size={40}
            style={[styles.icon, isUnread && {backgroundColor: colors.primary}]}
            color={isUnread ? '#fff' : '#000'}
          />
        </View>

        <View style={styles.textContainer}>
          <Text style={[styles.title, isUnread && {fontWeight: 'bold'}]}>
            {item.notification?.subject}
          </Text>
          <Text numberOfLines={2} style={styles.message}>
            {item.notification?.body}
          </Text>
          <Text style={styles.date}>{dayjs(item.createdOn).fromNow()}</Text>
        </View>

        {isUnread && <View style={styles.unreadDot} />}
      </TouchableOpacity>
    );
  };

  const safeNotifications = notifications || [];
  return (
    <View style={styles.container}>
      {safeNotifications.length > 0 ? (
        <FlatList
          data={safeNotifications}
          keyExtractor={(item, index) => item.id ?? index.toString()}
          renderItem={renderItem}
          contentContainerStyle={styles.list}
        />
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No notifications right now.</Text>
        </View>
      )}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <ScrollView>
              <Text style={styles.modalTitle}>
                {selectedNotification?.notification?.subject}
              </Text>
              <Text style={styles.modalBody}>
                {selectedNotification?.notification?.body}
              </Text>

              <Text style={styles.subscriptionText}>
                Would you like to subscribe to receive updates and latest
                information about this topic?
              </Text>
            </ScrollView>

            <View style={styles.buttonRow}>
              <CustomButton
                mode="outlined"
                title="No, Just Mark as Read"
                onPress={handleMarkAsRead}
              />
              <CustomButton title="Yes, Subscribe" onPress={handleSubscribe} />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default NotificationScreen;
const styles = StyleSheet.create({
  emptyState: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  emptyText: {fontSize: 16, color: '#999'},
  container: {flex: 1, backgroundColor: '#f5f5f5'},
  list: {padding: 16},
  card: {
    flexDirection: 'row',
    padding: 14,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  iconContainer: {
    marginRight: 12,
  },
  icon: {
    backgroundColor: '#e0e0e0',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    marginBottom: 2,
    color: '#333',
  },
  message: {
    fontSize: 14,
    color: '#666',
  },
  date: {
    fontSize: 12,
    color: '#999',
    marginTop: 6,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 10,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  modalBody: {
    fontSize: 16,
    marginBottom: 20,
  },
  subscriptionText: {
    fontSize: 15,
    marginVertical: 20,
    color: '#555',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
