import {
  Binding<PERSON><PERSON>,
  Getter,
  inject,
  injectable,
  service,
  Setter,
} from '@loopback/core';
import {
  FilterExcludingWhere,
  IsolationLevel,
  repository,
} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {Transaction} from '@loopback/sequelize';
import {
  AuthClientRepository,
  AuthCodeBindings,
  AuthenticationBindings,
  AuthServiceBindings,
  CodeWriterFn,
  PasswordHashingFn,
  RoleRepository,
  TenantRepository,
  User,
  UserRepository,
  UserTenantRepository,
  UserTenantWithRelations,
} from '@sourceloop/authentication-service';
import {
  IAuthUserWithPermissions,
  ModifiedRestService,
  restService,
  RoleTypes,
  UserStatus,
} from '@sourceloop/core';
import * as fs from 'fs';
import * as handlebars from 'handlebars';
import * as jwt from 'jsonwebtoken';
import {AuthErrorKeys} from 'loopback4-authentication';
import {MessageType} from 'loopback4-notifications';
import * as path from 'path';
import {systemUser} from '../constants';
import {PermissionKey} from '../enums';
import {RoleType} from '../enums/role-type.enum';
import {SellerStatus} from '../enums/seller-status.enum';
import {
  Admin,
  Customer,
  IAuthUserWithTenant,
  Notification,
  Seller,
  SignUpDto,
} from '../models';
import {
  AdminRepository,
  CustomerRepository,
  SellerRepository,
} from '../repositories';
import {Msg91SMSType} from '../types';
import {SellerService} from './seller.service';
import {S3Service} from './s3.service';
import {CustomerService} from './customer.service';

@injectable({scope: BindingScope.TRANSIENT})
export class AuthHelperService {
  constructor(
    @repository(UserRepository) private readonly userRepository: UserRepository,
    @repository(AuthClientRepository)
    private readonly authClientRepository: AuthClientRepository,
    @repository(RoleRepository)
    private readonly roleRepository: RoleRepository,
    @repository(TenantRepository)
    private readonly tenantRepository: TenantRepository,
    @inject(AuthServiceBindings.PASSWORD_HASHING_PROVIDER)
    private readonly passwordHashingFn: PasswordHashingFn,
    @service(SellerService)
    private readonly sellerService: SellerService,
    @inject(RestBindings.Http.REQUEST)
    private readonly req: Request,
    @restService(Notification)
    private readonly notification: ModifiedRestService<Notification>,
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
    @repository(SellerRepository)
    private readonly sellerRepo: SellerRepository,
    @inject.setter(AuthenticationBindings.CURRENT_USER)
    private readonly setCurrentUser: Setter<IAuthUserWithTenant>,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @repository(UserTenantRepository)
    private readonly userTenantRepo: UserTenantRepository,
    @service(S3Service)
    private readonly s3Service: S3Service,
    @service(CustomerService)
    private readonly customerService: CustomerService,
    @service(CustomerRepository)
    private readonly customerRepo: CustomerRepository,
    @service(AdminRepository)
    private readonly adminRepo: AdminRepository,
  ) {}

  async signupUsingLocalEmail(signupDto: SignUpDto): Promise<SignUpDto> {
    const xOrigin = this.req.headers['x-origin'];

    // Determine role type based on x-origin
    const roleType = this.getRoleType(xOrigin as string);
    const tx = await this.userRepository.dataSource.beginTransaction(
      IsolationLevel.READ_COMMITTED,
    );
    const {firstName, lastName, email, password, phoneNumber} = signupDto;

    const [tenant, role, client] = await Promise.all([
      this.tenantRepository.findOne({
        where: {key: 'ecomdukes'},
      }),
      this.roleRepository.findOne({
        where: {roleType: roleType as unknown as RoleTypes},
      }),
      this.authClientRepository.findOne({
        where: {clientId: 'email_password_client'},
      }),
    ]);

    if (!client) {
      throw new HttpErrors.Unauthorized('Invalid client ID');
    }

    // Check if user already exists
    const userExists = await this.userRepository.findOne({
      where: {
        or: [{username: email.toLowerCase()}, {email: email.toLowerCase()}],
      },
    });
    if (userExists) {
      throw new HttpErrors.Conflict('Email is already registered');
    }
    try {
      // Create user
      const user = await this.userRepository.createWithoutPassword(
        {
          firstName,
          lastName,
          phone: phoneNumber,
          username: email.toLowerCase(),
          email: email.toLowerCase(),
          defaultTenantId: tenant?.id,
          authClientIds: `{${client?.id}}`,
        },
        {transaction: tx},
      );

      // Hash password and store credentials
      const hashedPassword = await this.passwordHashingFn(password);
      await this.userRepository.credentials(user.id).create(
        {
          userId: user.id,
          authProvider: 'internal',
          password: hashedPassword,
        },
        {transaction: tx},
      );

      const userTenant = await this.userRepository.userTenants(user.id).create(
        {
          userId: user.id,
          tenantId: tenant?.id,
          status: UserStatus.ACTIVE,
          roleId: role?.id,
        },
        {transaction: tx},
      );

      if (roleType === RoleType.SELLER) {
        await this.sellerService.createNewSeller(
          {
            emailVerified: false,
            phoneVerified: false,
            status: SellerStatus.PENDING,
            userTenantId: userTenant.id,
          },
          tx as Transaction,
        );
      } else if (roleType === RoleType.CUSTOMER) {
        await this.customerService.createNewCustomer(
          {
            emailVerified: false,
            phoneVerified: false,
            userTenantId: userTenant.id,
          },
          tx as Transaction,
        );
      }

      await tx.commit();
      return signupDto;
    } catch (error) {
      await tx.rollback();
      throw error;
    }
  }

  getRoleType(origin: string): RoleType {
    const originRoleTypeMap: Record<string, RoleType> = {
      ['ecomdukes-seller']: RoleType.SELLER,
      ['ecomdukes-customer']: RoleType.CUSTOMER,
      ['ecomdukes-admin']: RoleType.ADMIN,
    };
    return originRoleTypeMap[origin] ?? RoleType.GUEST;
  }

  getRoleTypeByClient(client: string): RoleType {
    const originRoleTypeMap: Record<string, RoleType> = {
      ['google_seller_client']: RoleType.SELLER,
      ['facebook_seller_client']: RoleType.SELLER,
    };
    return originRoleTypeMap[client] ?? RoleType.GUEST;
  }

  async sendOtp(type: MessageType, otp: string, user: User) {
    const codePayload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKey.CreateNotification,
        PermissionKey.CreateNotificationNum,
      ],
    };
    const token = await this.codeWriter(
      jwt.sign({...codePayload}, process.env.JWT_SECRET as string, {
        expiresIn: 3600,
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );

    if (type === MessageType.Email) {
      const emailData = {
        productName: 'Ecomdukes',
        OTP: otp,
        validTime: '5 Minutes',
        supportId: '<EMAIL>',
        brand: 'Ecomdukes',
      };
      const templatePath = path.resolve(
        __dirname,
        '../templates',
        `verification-email.hbs`,
      );

      // Read template file
      const templateSource = fs.readFileSync(templatePath, 'utf8');
      // Compile Handlebars template
      const template = handlebars.compile(templateSource);
      const notification = {
        body: template(emailData).toString(),
        type: MessageType.Email,
        subject: 'Your OTP Code for Ecomdukes Verification',
        receiver: {
          to: [
            {
              id: user.email,
              name: `${user.firstName} ${user.lastName}`,
            },
          ],
        },
      };

      // Generate final HTML with data
      await this.notification.create(notification, `Bearer ${token}`);
    } else if (type === MessageType.SMS) {
      const notification = {
        body: otp,
        type: MessageType.SMS,
        subject: 'Your OTP Code for Ecomdukes Verification',
        smsType: Msg91SMSType.Otp,
        receiver: {
          to: [
            {
              id: user.phone,
              name: `${user.firstName} ${user.lastName}`,
            },
          ],
        },
      };

      // Generate final HTML with data
      await this.notification.create(notification, `Bearer ${token}`);
    }
  }

  async mark2FAVerified(type: MessageType, user: User): Promise<void> {
    const userTenantId = user.userTenants[0]?.id;
    const origin = this.req.headers['x-origin'];
    const roleType = this.getRoleType(origin as string);
    this.setCurrentUser({
      ...user,
      permissions: [],
      profileId: '',
    } as unknown as IAuthUserWithTenant);
    if (roleType === RoleType.SELLER) {
      const payload: Partial<Seller> =
        Number(type) === MessageType.Email
          ? {emailVerified: true}
          : {phoneVerified: true};
      const seller = await this.sellerRepo.findOne({where: {userTenantId}});
      await this.sellerRepo.updateById(seller?.id ?? '', payload);
    }
  }

  async getProfile(
    roleType: RoleType,
    userTenantId: string,
  ): Promise<Seller | Customer | Admin | null> {
    switch (roleType) {
      case RoleType.SELLER:
        return this.sellerRepo.findOne({
          include: [{relation: 'sellerStore'}],
          where: {userTenantId},
        } as FilterExcludingWhere<Seller>);
      case RoleType.CUSTOMER:
        return this.customerRepo.findOne({where: {userTenantId}});
      case RoleType.ADMIN:
        return this.adminRepo.findOne({
          where: {userTenantId},
        } as FilterExcludingWhere<Admin>);
      default:
        return null;
    }
  }

  getProfileDisplayId(
    roleType: RoleType,
    profile: Seller | Customer | Admin | null,
  ): string {
    switch (roleType) {
      case RoleType.SELLER:
        return (profile as Seller)?.sellerId ?? '';
      case RoleType.CUSTOMER:
        return (profile as Customer)?.customerId ?? '';
      default:
        return '';
    }
  }

  getOnboardCompleteFalg(
    roleType: RoleType,
    profile: Seller | Customer | Admin | null,
  ): boolean {
    switch (roleType) {
      case RoleType.SELLER:
        return Boolean(
          (profile as Seller)?.sellerStore.id &&
            !(profile as Seller)?.sellerStore.deleted,
        );
      default:
        return true;
    }
  }

  private veirfyorigin(userTenant: UserTenantWithRelations, origin: string) {
    const originAllowedRoleTypeMap: Record<string, RoleType[]> = {
      ['ecomdukes-seller']: [RoleType.SELLER],
      ['ecomdukes-customer']: [RoleType.CUSTOMER, RoleType.SELLER],
      ['ecomdukes-admin']: [RoleType.ADMIN, RoleType.SUB_ADMIN],
    };
    if (
      !originAllowedRoleTypeMap[origin].includes(
        userTenant.role.roleType as unknown as RoleType,
      )
    ) {
      throw new HttpErrors.Forbidden(
        'You are not authorized to access this application',
      );
    }
  }

  async getMe(origin: string) {
    const user = await this.getCurrentUser();
    if (!user) {
      throw new HttpErrors.Unauthorized(AuthErrorKeys.TokenInvalid);
    }
    const currentUser = await this.userRepository.findById(user.id);
    const roleType = this.getRoleType(origin as string);

    const userTenant = await this.userTenantRepo.findOne({
      where: {userId: user.id, tenantId: user.defaultTenantId},
      include: [
        {relation: 'role', scope: {fields: {roleType: true, id: true}}},
      ],
    });

    if (!userTenant) {
      throw new HttpErrors.Unauthorized('User Tenant not found');
    }
    this.veirfyorigin(userTenant, origin);
    const profile = await this.getProfile(
      Number(roleType),
      userTenant.id ?? '',
    );

    const photoUrl = await this.s3Service.getPresignedUrl(
      currentUser?.photoUrl ?? '',
    );

    const modifiedUser = {
      ...user,
      ...currentUser,
      photoUrl,
      profileId: profile?.id ?? '',
      emailVerified: profile?.emailVerified,
      phoneVerified: profile?.phoneVerified,
      profileDisplayId: this.getProfileDisplayId(roleType, profile),
      profileStatus: profile?.status,
      rejectionReason: (profile as Seller)?.rejectionReason ?? '',
      onBoardComplete: this.getOnboardCompleteFalg(roleType, profile),
      profileVerificationCode: (profile as Seller)?.verificationCode,
      vendorId:
        roleType === RoleType.SELLER
          ? (profile as Seller)?.vendorId
          : undefined,
    };
    delete user.deviceInfo;
    return new IAuthUserWithTenant(modifiedUser);
  }
}
