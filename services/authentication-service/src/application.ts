import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication} from '@loopback/rest';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import {ServiceMixin} from '@loopback/service-proxy';
import {
  AuthCodeBindings,
  AuthenticationServiceComponent,
  AuthServiceBindings,
  BearerTokenVerifyProvider,
  JWTSymmetricSignerProvider,
  JWTSymmetricVerifierProvider,
  OtpMethodType,
  OtpProvider,
  SignUpBindings,
  VerifyBindings,
} from '@sourceloop/authentication-service';
import {
  BearerVerifierBindings,
  BearerVerifierComponent,
  BearerVerifierType,
  ProxyBuilderBindings,
  ProxyBuilderComponent,
  SECURITY_SCHEME_SPEC,
  ServiceSequence,
  SFCoreBindings,
} from '@sourceloop/core';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {
  AuthenticationComponent,
  Strategies,
  STRATEGY,
} from 'loopback4-authentication';
import {GoogleAuthStrategyFactoryProvider} from 'loopback4-authentication/passport-google-oauth2';
import {LocalPasswordStrategyFactoryProvider} from 'loopback4-authentication/passport-local';
import {
  AuthorizationBindings,
  AuthorizationComponent,
} from 'loopback4-authorization';
import path from 'path';
import * as openapi from './openapi.json';
import {
  AuthCodeGeneratorProvider,
  GoogleOauth2SignupProvider,
  GoogleOauth2VerifyProvider,
  MfaProvider,
  OtpGenerateProvider,
  OtpSenderProvider,
  OtpVerifyProvider,
  SignupTokenHandlerProvider,
} from './providers';
import {ForgotPasswordProvider} from './providers/forgot-password.provider';
import {LocalSignupProvider} from './providers/local-signup.provider';
// import {PgDataSource} from './datasources';
import {PassportOtpStrategyFactoryProvider} from 'loopback4-authentication/passport-otp';
import {AWSS3Bindings, AwsS3Component} from 'loopback4-s3';
import {Notification, Plan, Subscription} from './models';
import {GoogleProvider, OtpService} from './services';
import {FileUtilBindings, FileUtilComponent} from '@sourceloop/file-utils';
import {
  FileUploadLimitsService,
  CoreComponent,
  FileValidatorService,
  MulterS3Storage,
} from '@local/core';

export {ApplicationConfig};

export class AuthenticationApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    const port = 3000;
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });
    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };

    super(options);

    // To check if monitoring is enabled from env or not
    const enableObf = !!+(process.env.ENABLE_OBF ?? 0);
    // To check if authorization is enabled for swagger stats or not
    const authentication =
      process.env.SWAGGER_USER && process.env.SWAGGER_PASSWORD ? true : false;
    const obj = {
      enableObf,
      obfPath: process.env.OBF_PATH ?? '/obf',
      openapiSpec: openapi,
      authentication: authentication,
      swaggerUsername: process.env.SWAGGER_USER,
      swaggerPassword: process.env.SWAGGER_PASSWORD,
    };
    this.bind(SFCoreBindings.config).to(obj);

    // Set up the custom sequence
    this.sequence(ServiceSequence);

    // Add authentication component
    this.component(AuthenticationComponent);
    this.bind(AuthServiceBindings.Config).to({
      useCustomSequence: false,
      useSequelize: true,
      useSymmetricEncryption: true,
    });
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.service,
      useSymmetricEncryption: true,
    });
    this.bind(AuthServiceBindings.OtpConfig).to({method: OtpMethodType.OTP});

    this.component(AuthenticationServiceComponent);
    this.bind(
      Strategies.Passport.GOOGLE_OAUTH2_STRATEGY_FACTORY.key,
    ).toProvider(GoogleAuthStrategyFactoryProvider);
    this.bind(Strategies.Passport.GOOGLE_OAUTH2_VERIFIER.key).toProvider(
      GoogleOauth2VerifyProvider,
    );
    // this.providers[Strategies.Passport.GOOGLE_OAUTH2_VERIFIER.key] =
    //   GoogleOauth2VerifyProvider;
    this.bind(SignUpBindings.SIGNUP_HANDLER_PROVIDER).toProvider(
      SignupTokenHandlerProvider,
    );
    this.bind(SignUpBindings.LOCAL_SIGNUP_PROVIDER).toProvider(
      LocalSignupProvider,
    );

    this.bind(AuthServiceBindings.ForgotPasswordHandler.key).toProvider(
      ForgotPasswordProvider,
    );

    this.bind(Strategies.Passport.LOCAL_STRATEGY_FACTORY).toProvider(
      LocalPasswordStrategyFactoryProvider,
    );
    // Add bearer verifier component
    this.component(BearerVerifierComponent);
    this.bind(SignUpBindings.GOOGLE_SIGN_UP_PROVIDER).toProvider(
      GoogleOauth2SignupProvider,
    );
    this.bind(AuthCodeBindings.JWT_SIGNER).toProvider(
      JWTSymmetricSignerProvider,
    );

    this.bind(AuthCodeBindings.JWT_VERIFIER).toProvider(
      JWTSymmetricVerifierProvider,
    );
    this.bind(Strategies.Passport.BEARER_TOKEN_VERIFIER).toProvider(
      BearerTokenVerifyProvider,
    );
    this.bind('services.GoogleService').toProvider(GoogleProvider);
    // Add authorization component
    this.bind(AuthorizationBindings.CONFIG).to({
      allowAlwaysPaths: ['/explorer', '/openapi.json'],
    });
    this.component(AuthorizationComponent);

    this.bind(VerifyBindings.OTP_GENERATE_PROVIDER.key).toProvider(
      OtpGenerateProvider,
    );

    this.bind(VerifyBindings.OTP_PROVIDER.key).toProvider(OtpProvider);

    this.bind(VerifyBindings.OTP_SENDER_PROVIDER.key).toProvider(
      OtpSenderProvider,
    );

    this.bind(Strategies.Passport.OTP_VERIFIER.key).toProvider(
      OtpVerifyProvider,
    );

    this.bind(Strategies.Passport.OTP_AUTH_STRATEGY_FACTORY.key).toProvider(
      PassportOtpStrategyFactoryProvider,
    );
    this.bind('services.otpService').toClass(OtpService);
    this.bind(VerifyBindings.MFA_PROVIDER.key).toProvider(MfaProvider);
    this.bind(AuthServiceBindings.MfaConfig).to({secondFactor: STRATEGY.OTP});
    this.bind(ProxyBuilderBindings.CONFIG).to([
      {
        baseUrl: process.env.NOTIFICATION_SERVICE_URL as string,
        configs: [
          {
            model: Notification,
            basePath: '/notifications',
          },
        ],
      },
      {
        baseUrl: process.env.ECOM_SERVICE_URL as string,
        configs: [
          {
            model: Plan,
            basePath: '/plans',
          },
          {
            model: Subscription,
            basePath: '/subscriptions',
          },
        ],
      },
    ]);
    this.component(ProxyBuilderComponent);
    this.bind(AuthCodeBindings.AUTH_CODE_GENERATOR_PROVIDER.key).toProvider(
      AuthCodeGeneratorProvider,
    );
    this.component(FileUtilComponent);
    this.bind(FileUtilBindings.LimitProvider).toClass(FileUploadLimitsService);
    this.service(MulterS3Storage);

    this.component(AwsS3Component);
    this.bind(AWSS3Bindings.Config).to({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID ?? '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? '',
      region: process.env.AWS_REGION,
    });
    this.component(CoreComponent);
    this.bind('services.FileValidatorService').toClass(FileValidatorService);
    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));
    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });

    this.component(RestExplorerComponent);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };

    this.api({
      openapi: '3.0.0',
      info: {
        title: 'authentication',
        version: '1.0.0',
      },
      paths: {},
      components: {
        securitySchemes: SECURITY_SCHEME_SPEC,
      },
      servers: [{url: '/'}],
    });
  }
}
