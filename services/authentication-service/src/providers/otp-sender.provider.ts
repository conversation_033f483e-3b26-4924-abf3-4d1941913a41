import {Getter, inject, Provider} from '@loopback/context';
import {RestBindings, Request} from '@loopback/rest';
import {OtpSenderFn, User} from '@sourceloop/authentication-service';
import {AuthHelperService} from '../services';
import {MessageType} from 'loopback4-notifications';
import {ILogger, LOGGER} from '@sourceloop/core';
export class OtpSenderProvider implements Provider<OtpSenderFn> {
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject.getter('services.AuthHelperService')
    private readonly getAuthHelperService: Getter<AuthHelperService>,
    @inject(LOGGER.LOGGER_INJECT) public logger: ILogger,
  ) {}
  value(): OtpSenderFn {
    return async (otp: string, user: User) => {
      await this.sendOtp(
        this.request.query.type as unknown as MessageType,
        otp,
        user,
      );
    };
  }

  private async sendOtp(type: MessageType, otp: string, user: User) {
    try {
      const authHelperService = await this.getAuthHelperService();
      await authHelperService.sendOtp(Number(type), otp, user);
    } catch (error) {
      this.logger.error(error);
      // do nothing
    }
  }
}
