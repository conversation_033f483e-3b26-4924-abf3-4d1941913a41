import {Getter, inject, Provider} from '@loopback/context';
import {repository} from '@loopback/repository';
import {HttpErrors, RestBindings, Request} from '@loopback/rest';
import {
  AuthClient,
  OtpCacheRepository,
  OtpService,
  UserRepository,
} from '@sourceloop/authentication-service';
import {ILogger, LOGGER} from '@sourceloop/core';
import {
  AuthenticationBindings,
  AuthErrorKeys,
  VerifyFunction,
} from 'loopback4-authentication';
import {hotp} from 'otplib';
import {AuthHelperService} from '../services';
import {MessageType} from 'loopback4-notifications';
import {OtpRetryRepository} from '../repositories';

export class OtpVerifyProvider implements Provider<VerifyFunction.OtpAuthFn> {
  private readonly MAX_RETRY_COUNT = 3;
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject(LOGGER.LOGGER_INJECT) private readonly logger: ILogger,
    @inject(AuthenticationBindings.CURRENT_CLIENT)
    private readonly client: AuthClient,
    @inject('services.otpService')
    private readonly otpService: OtpService,
    @repository(OtpCacheRepository)
    public otpCacheRepo: OtpCacheRepository,
    @inject.getter('services.AuthHelperService')
    private readonly getAuthHelperService: Getter<AuthHelperService>,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @repository(OtpRetryRepository)
    public otpRetryRepo: OtpRetryRepository,
  ) {}

  value(): VerifyFunction.OtpAuthFn {
    return async (username: string, otp: string) => {
      const user = await this.userRepository.findOne({
        where: {
          username: username,
        },
        include: [{relation: 'userTenants'}],
      });

      if (!user) {
        this.logger.error('Invalid Username');
        throw new HttpErrors.Unauthorized(AuthErrorKeys.InvalidCredentials);
      }

      //sender
      if (!otp) {
        await this.otpService.sendOtp(user, this.client);
        return user;
      }

      const retryKey = `${username}-maxRetryCount`;

      // Retrieve retry count from Redis
      const retryData = await this.otpRetryRepo.get(retryKey);
      let retryCount = retryData?.count ?? 0;

      // Check retry limit
      if (retryCount >= this.MAX_RETRY_COUNT) {
        this.logger.error(
          `User ${username} has exceeded the maximum number of OTP verification attempts.`,
        );
        await this.otpRetryRepo.delete(retryKey);
        await this.otpCacheRepo.delete(username);
        throw new HttpErrors.Unauthorized(
          'You have exceeded the maximum number of OTP verification attempts. Please request a new OTP and try again.',
        );
      }

      //verifier
      const otpCache = await this.otpCacheRepo.get(username);
      if (!otpCache) {
        retryCount += 1;
        await this.otpRetryRepo.delete(retryKey);
        this.logger.error('Invalid Username');
        throw new HttpErrors.Unauthorized(AuthErrorKeys.OtpExpired);
      }
      let isValid = false;
      try {
        if (otpCache.otpSecret)
          isValid = hotp.check(otp, otpCache.otpSecret, 100);
        await this.otpCacheRepo.set(username, otpCache);
        const authService = await this.getAuthHelperService();
        if (isValid) {
          await this.otpRetryRepo.delete(retryKey);
          await authService.mark2FAVerified(
            this.request.query.type as unknown as MessageType,
            user,
          );
        }
      } catch (err) {
        this.logger.error(err);
        throw new HttpErrors.Unauthorized(AuthErrorKeys.InvalidCredentials);
      }
      if (!isValid) {
        throw new HttpErrors.Unauthorized(AuthErrorKeys.OtpInvalid);
      }
      return user;
    };
  }
}
