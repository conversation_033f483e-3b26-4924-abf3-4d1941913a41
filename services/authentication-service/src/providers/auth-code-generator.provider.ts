import {inject, Provider} from '@loopback/context';
import {AnyObject} from '@loopback/repository';
import {
  AuthClient,
  AuthCodeBindings,
  AuthCodeGeneratorFn,
  AuthUser,
  CodeWriterFn,
  JWTSignerFn,
  User,
} from '@sourceloop/authentication-service';
import {ClientAuthCode} from 'loopback4-authentication';

export class AuthCodeGeneratorProvider
  implements Provider<AuthCodeGeneratorFn>
{
  constructor(
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
    @inject(AuthCodeBindings.JWT_SIGNER.key)
    private readonly jwtSigner: JWTSignerFn<AnyObject>,
  ) {}

  value(): AuthCodeGeneratorFn {
    return async (client: AuthClient, user: AuthUser) => {
      const codePayload: ClientAuthCode<User, typeof User.prototype.id> = {
        clientId: client.clientId,
        user: user,
      };

      const token = await this.jwtSigner(codePayload, {
        audience: client.clientId,
        expiresIn: client.authCodeExpiration,
      });

      return this.codeWriter(token);
    };
  }
}
