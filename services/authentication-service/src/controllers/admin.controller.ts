import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Admin, AdminDto, AdminWithRelations} from '../models';
import {AdminRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {service} from '@loopback/core';
import {AdminService, S3Service} from '../services';
import {PermissionKeys} from '@local/core';
import {UserLevelPermissionRepository} from '@sourceloop/authentication-service';
const basePath = '/admins';
export class AdminController {
  constructor(
    @repository(AdminRepository)
    public adminRepository: AdminRepository,
    @service(AdminService)
    private adminService: AdminService,
    @repository(UserLevelPermissionRepository)
    public userLevelPermissionRepository: UserLevelPermissionRepository,
    @service(S3Service)
    private readonly s3Service: S3Service,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSubAdmin]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Admin model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Admin)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(AdminDto, {
            title: 'NewAdmin',
            exclude: ['id'],
          }),
        },
      },
    })
    admin: Omit<AdminDto, 'id'>,
  ): Promise<Admin> {
    return this.adminService.createAdmin(admin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSubAdmin]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Admin model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Admin) where?: Where<Admin>): Promise<Count> {
    return this.adminRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSubAdmin]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Admin model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Admin, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Admin) filter?: Filter<Admin>,
  ): Promise<AdminWithRelations[]> {
    const admins = await this.adminRepository.find(filter);

    const adminsWithDetails = await Promise.all(
      admins.map(async admin => {
        const permissions = await this.userLevelPermissionRepository.find({
          where: {userTenantId: admin.userTenantId},
        });

        return {
          ...admin,
          permissions,
        };
      }),
    );

    return adminsWithDetails as AdminWithRelations[];
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSubAdmin]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Admin PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Admin, {partial: true}),
        },
      },
    })
    admin: Admin,
    @param.where(Admin) where?: Where<Admin>,
  ): Promise<Count> {
    return this.adminRepository.updateAll(admin, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSubAdmin]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Admin model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Admin, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Admin, {exclude: 'where'}) filter?: Filter<Admin>,
  ): Promise<AdminWithRelations> {
    const admin = await this.adminRepository.findById(id, filter);

    const permissions = await this.userLevelPermissionRepository.find({
      where: {userTenantId: admin.userTenantId},
    });
    return {
      ...admin,
      permissions,
    } as AdminWithRelations;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSubAdmin]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(AdminDto, {partial: true}),
        },
      },
    })
    admin: Partial<AdminDto>,
  ): Promise<void> {
    await this.adminService.updateAdmin(id, admin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSubAdmin]})
  @patch(`${basePath}/{id}/status`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin status update success',
  })
  async updateAdminStatus(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['status'],
            properties: {
              status: {
                type: 'string',
                enum: ['PENDING', 'APPROVED', 'REJECTED', 'INACTIVE'],
              },
            },
          },
        },
      },
    })
    statusUpdate: {status: string},
  ): Promise<void> {
    await this.adminService.updateAdminStatus(id, statusUpdate.status as any);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSubAdmin]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() admin: Admin,
  ): Promise<void> {
    await this.adminRepository.replaceById(id, admin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSubAdmin]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Admin DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.adminService.deleteAdmin(id);
  }
}
